# libwolfssl.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-15build2
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libwolfssl.so.24'

# Names of this library.
library_names='libwolfssl.so.24.5.1 libwolfssl.so.24 libwolfssl.so'

# The name of the static archive.
old_library='libwolfssl.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -lm -lpthread'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libwolfssl.
current=29
age=5
revision=1

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_chuangsan/build/../../../../lib/libwolfssl/install/lib'
