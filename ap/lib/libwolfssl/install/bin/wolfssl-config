#! /bin/sh
 
package="wolfssl"
version="4.8.1"
libs="-lwolfssl -lpthread"
 
prefix="/home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_chuangsan/build/../../../../lib/libwolfssl/install"
exec_prefix="${prefix}"
bindir="${exec_prefix}/bin"
sbindir="${exec_prefix}/sbin"
libexecdir="${exec_prefix}/libexec"
datadir="${datarootdir}"
sysconfdir="${prefix}/etc"
sharedstatedir="${prefix}/com"
localstatedir="${prefix}/var"
libdir="${exec_prefix}/lib"
infodir="${datarootdir}/info"
mandir="${datarootdir}/man"
includedir="${prefix}/include"
target="arm-unknown-linux-gnu"
host="arm-unknown-linux-gnu"
build="x86_64-unknown-linux-gnu"
 
if test "$#" -eq 0; then
   cat <<EOF
Usage: $package-config OPTIONS
Options:
  --prefix=DIR) : \$prefix
  --package) : \$package
  --version) : \$version
  --cflags) : -I\$includedir
  --libs) : -L\$libdir -l\$package
  --help) print all the options (not just these)
EOF
fi
 
o=""
h=""
for i in "$@"; do
  case $i in
  --prefix=*) prefix=`echo $i | sed -e "s/--prefix=//"` ;;
  --prefix)    o="$o $prefix" ;;
  --package)   o="$o $package" ;;
  --version)   o="$o $version" ;;
  --cflags) if test "_$includedir" != "_/usr/include"
          then o="$o -I$includedir" ; fi
  ;;
  --libs)      o="$o -L$libdir $libs" ;;
  --exec_prefix|--eprefix) o="$o $exec_prefix" ;;
  --bindir)                o="$o $bindir" ;;
  --sbindir)               o="$o $sbindir" ;;
  --libexecdir)            o="$o $libexecdir" ;;
  --datadir)               o="$o $datadir" ;;
  --datainc)               o="$o -I$datadir" ;;
  --datalib)               o="$o -L$datadir" ;;
  --sysconfdir)            o="$o $sysconfdir" ;;
  --sharedstatedir)        o="$o $sharedstatedir" ;;
  --localstatedir)         o="$o $localstatedir" ;;
  --libdir)                o="$o $libdir" ;;
  --libadd)                o="$o -L$libdir" ;;
  --infodir)               o="$o $infodir" ;;
  --mandir)                o="$o $mandir" ;;
  --target)                o="$o $target" ;;
  --host)                  o="$o $host" ;;
  --build)                 o="$o $build" ;;
  --data)                  o="$o -I$datadir/$package" ;;
  --pkgdatadir)            o="$o $datadir/$package" ;;
  --pkgdatainc)            o="$o -I$datadir/$package" ;;
  --pkgdatalib)            o="$o -L$datadir/$package" ;;
  --pkglibdir)             o="$o $libdir/$package" ;;
  --pkglibinc)             o="$o -I$libinc/$package" ;;
  --pkglibadd)             o="$o -L$libadd/$package" ;;
  --pkgincludedir)         o="$o $includedir/$package" ;;
  --help) h="1" ;;
  -?//*|-?/*//*|-?./*//*|//*|/*//*|./*//*) 
       v=`echo $i | sed -e s://:\$:g`
       v=`eval "echo $v"` 
       o="$o $v" ;; 
  esac
done
 
o=`eval "echo $o"`
o=`eval "echo $o"`
eval "echo $o"
 
if test ! -z "$h" ; then 
cat <<EOF
  --prefix=xxx)      (what is that for anyway?)
  --prefix)         \$prefix        $prefix
  --package)        \$package       $package
  --version)        \$version       $version
  --cflags)         -I\$includedir    unless it is /usr/include
  --libs)           -L\$libdir -l\$PACKAGE \$LIBS
  --exec_prefix) or... 
  --eprefix)        \$exec_prefix   $exec_prefix
  --bindir)         \$bindir        $bindir
  --sbindir)        \$sbindir       $sbindir
  --libexecdir)     \$libexecdir    $libexecdir
  --datadir)        \$datadir       $datadir
  --sysconfdir)     \$sysconfdir    $sysconfdir
  --sharedstatedir) \$sharedstatedir$sharedstatedir
  --localstatedir)  \$localstatedir $localstatedir
  --libdir)         \$libdir        $libdir
  --infodir)        \$infodir       $infodir
  --mandir)         \$mandir        $mandir
  --target)         \$target        $target
  --host)           \$host          $host
  --build)          \$build         $build
  --data)           -I\$datadir/\$package
  --pkgdatadir)     \$datadir/\$package
  --pkglibdir)      \$libdir/\$package
  --pkgincludedir)  \$includedir/\$package
  --help)           generated by ax_create_generic_config.m4
  -I//varname and other inc-targets like --pkgdatainc supported
  -L//varname and other lib-targets, e.g. --pkgdatalib or --libadd
EOF
fi
