#!/bin/bash

if [ $# -ne 4 ]; then
    echo "$0 error"
    echo "Usage:"
    echo "        $0 mtd_name outfile image_file ubinize_para"
    exit  -1
fi

mtdname=$1
outfile=$2
imgfile=$3
ubinize_para=$4

filesize="$(stat -c%s $imgfile)"

cfg_file_name=$mtdname.cfg

echo "[$mtdname]"                     > $cfg_file_name
echo "mode=ubi"                      >> $cfg_file_name
echo "image=$imgfile"                >> $cfg_file_name
echo "vol_id=0"                      >> $cfg_file_name
echo "vol_size=${filesize}"          >> $cfg_file_name
echo "vol_type=dynamic"               >> $cfg_file_name
echo "vol_name=$mtdname"             >> $cfg_file_name

cat $cfg_file_name

./ubinize  -o $outfile $ubinize_para $cfg_file_name
if [ $? -ne 0 ]; then
    echo "ubinize error"
    exit -2
else
    echo "ubinize ok"
fi

rm -v $cfg_file_name
