#!/bin/bash

#*******************************************************************************
# QRZL App 原版本恢复脚本
# 用于恢复原有的qrzl_app构建
#*******************************************************************************

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
QRZL_APP_DIR="$(dirname "$SCRIPT_DIR")/qrzl_app"

echo "=== QRZL App 原版本恢复脚本 ==="
echo "原版本目录: $QRZL_APP_DIR"

# 检查目录是否存在
if [ ! -d "$QRZL_APP_DIR" ]; then
    echo "错误: 原版本目录不存在: $QRZL_APP_DIR"
    exit 1
fi

# 恢复原有的Makefile
echo "1. 恢复原有的Makefile..."
if [ -f "$QRZL_APP_DIR/Makefile.original" ]; then
    cp "$QRZL_APP_DIR/Makefile.original" "$QRZL_APP_DIR/Makefile"
    echo "   已恢复: Makefile.original -> Makefile"
else
    echo "   警告: 未找到备份的Makefile.original"
fi

# 删除符号链接
echo "2. 删除重构版本符号链接..."
if [ -L "$QRZL_APP_DIR/qrzl_app_refactored" ]; then
    rm "$QRZL_APP_DIR/qrzl_app_refactored"
    echo "   已删除符号链接: $QRZL_APP_DIR/qrzl_app_refactored"
fi

echo "=== 恢复完成 ==="
echo ""
echo "现在可以使用原版本进行编译:"
echo "cd /home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build"
echo "make app RF_TYPE=230A DCXO=yes"
