#*******************************************************************************
# QRZL App 重构版本 - 生产环境构建文件
# 用于替换原有的 qrzl_app 构建，支持xunji_cloud_client_start
#*******************************************************************************
include $(COMMON_MK)

EXEC = qrzl_app

# 重构版本的源文件目录
REFACTORED_DIR = $(APP_DIR)/qrzl_app_refactored

# 重构版本的对象文件
OBJS_BASE = $(REFACTORED_DIR)/src/qrzl_main.o \
		$(REFACTORED_DIR)/src/qrzl_timer_libsoft.o \
		$(REFACTORED_DIR)/src/qrzl_thread_mgr.o \
		$(REFACTORED_DIR)/src/qrzl_signal_handler.o \
		$(REFACTORED_DIR)/src/qrzl_network_client.o \
		$(REFACTORED_DIR)/src/qrzl_device_control.o \
		$(REFACTORED_DIR)/src/qrzl_utils.o \
		$(REFACTORED_DIR)/common_utils/cjson.o \
		$(REFACTORED_DIR)/common_utils/md5.o \
		$(REFACTORED_DIR)/common_utils/http_client.o

# FOTA 功能对象文件 (只在启用时编译)
OBJS_FOTA = 
ifdef QRZL_CS_FOTA
OBJS_FOTA += $(REFACTORED_DIR)/fota/fota_utils.o
endif

ifdef JCV_FEATURE_CAPTIVE_PORTAL_SERVER
OBJS_BASE += $(REFACTORED_DIR)/src/qrzl_captive_portal_server.o
endif

# 根据选择的云端协议添加对应的对象文件
OBJS_CLOUD =
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
# 使用重构版本的HTTP客户端，支持xunji_cloud_client_start
OBJS_CLOUD += $(REFACTORED_DIR)/src/modules/cloud_protocol/my_http_client.o
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
OBJS_CLOUD += $(REFACTORED_DIR)/common_utils/mqtt_client.o
endif

# 根据选择的认证方法添加对应的对象文件
OBJS_AUTH =
ifeq ($(QRZL_AUTH_ONE_LINK_HTTP),yes)
OBJS_AUTH += $(REFACTORED_DIR)/src/modules/auth_control/one_link_http_control.o
endif
ifeq ($(QRZL_AUTH_CMP_HTTP),yes)
OBJS_AUTH += $(REFACTORED_DIR)/src/modules/auth_control/cmp_auth_control.o
endif

# 根据选择的客户类型添加对应的认证控制对象文件
OBJS_AUTH_CONTROL =
ifeq ($(QRZL_AUTH_CUSTOMER),MY)
OBJS_AUTH_CONTROL += $(REFACTORED_DIR)/src/modules/auth_control/my_auth_control.o
endif

# 合并所有对象文件
OBJS = $(OBJS_BASE) $(OBJS_FOTA) $(OBJS_CLOUD) $(OBJS_AUTH) $(OBJS_AUTH_CONTROL)

# 宏和头文件目录在CFLAGS里定义，要用+=,不要用=,否则会覆盖COMMON_MK里的值
CFLAGS += -I$(APP_DIR)/include
CFLAGS += -I$(REFACTORED_DIR)/inc
CFLAGS += -I$(REFACTORED_DIR)/inc/modules
CFLAGS += -I$(REFACTORED_DIR)/common_utils
CFLAGS += -I$(zte_lib_path)/libsoftap
CFLAGS += -I$(zte_lib_path)/libsoft_timer
CFLAGS += -I$(zte_lib_path)/libatutils
CFLAGS += -I$(zte_lib_path)/libnvram

# 条件编译支持 - HTTP协议默认支持
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
CFLAGS += -DQRZL_ENABLE_HTTPS
CFLAGS += -I$(zte_lib_path)/libcurl/install/include
CFLAGS += -I$(zte_lib_path)/libwolfssl/install/include
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
CFLAGS += -DQRZL_ENABLE_MQTT
CFLAGS += -I$(zte_lib_path)/libpahomqttc/install/include
endif

# 添加编译优化选项以减小文件大小
CFLAGS += -Os -ffunction-sections -fdata-sections

# 添加动态链接和大小优化选项
LDFLAGS += -Wl,--as-needed -Wl,--gc-sections

# 公共库链接选项
LDLIBS = -lpthread -lm

LDLIBS_qrzl_app  = -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS_qrzl_app  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer
LDLIBS_qrzl_app  += -latutils -L$(zte_lib_path)/libatutils
LDLIBS_qrzl_app  += -lnvram -L$(zte_lib_path)/libnvram

# 条件链接库
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
LDLIBS_qrzl_app  += -lcurl -L$(zte_lib_path)/libcurl/install/lib/
LDLIBS_qrzl_app  += -lwolfssl -L$(zte_lib_path)/libwolfssl/install/lib/
endif
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
LDLIBS_qrzl_app  += -lpaho-mqtt3c -L$(zte_lib_path)/libpahomqttc/install/lib/
endif

#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ $(LDLIBS) $(LDLIBS_$@)
	@cp $@ $@.elf

romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)
ifeq ($(QRZL_USERDATA_OTA),yes)
	@cp -rvf $(PRJ_BIN_DIR)/../../../../../output/rootfs/bin/$(EXEC)  $(PRJ_BIN_DIR)/../../fs/normal/qrzl_ota/bin/
endif

clean:
	-@rm -f $(EXEC) *.elf *.gdb
	-@find $(REFACTORED_DIR) -name "*.o" -delete
