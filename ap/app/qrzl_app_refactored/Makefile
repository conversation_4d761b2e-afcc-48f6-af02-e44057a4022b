#*******************************************************************************
# QRZL App Refactored Makefile
# 重构后的 qrzl_app 构建文件
#*******************************************************************************

# 包含公共构建配置
include $(COMMON_MK)

# 可执行文件名
EXEC = qrzl_app

# 版本信息
VERSION_MAJOR = 2
VERSION_MINOR = 0
VERSION_PATCH = 0
VERSION = $(VERSION_MAJOR).$(VERSION_MINOR).$(VERSION_PATCH)

# 源文件目录
SRC_DIR = src
INC_DIR = inc
COMMON_UTILS_DIR = common_utils
MODULES_DIR = $(SRC_DIR)/modules

# 核心源文件
CORE_SRCS = $(SRC_DIR)/qrzl_main.c \
           $(SRC_DIR)/qrzl_timer_libsoft.c \
           $(SRC_DIR)/qrzl_thread_mgr.c \
           $(SRC_DIR)/qrzl_signal_handler.c \
           $(SRC_DIR)/qrzl_network_client.c \
           $(SRC_DIR)/qrzl_device_control.c \
           $(SRC_DIR)/qrzl_utils.c

# 公共工具源文件
COMMON_UTILS_SRCS = $(COMMON_UTILS_DIR)/cjson.c \
                   $(COMMON_UTILS_DIR)/http_client.c \
                   $(COMMON_UTILS_DIR)/mqtt_client.c \
                   $(COMMON_UTILS_DIR)/md5.c \
                   $(COMMON_UTILS_DIR)/tcp_client.c \
                   $(COMMON_UTILS_DIR)/xunji_protocol.c

# 模块源文件
CLOUD_PROTOCOL_SRCS = $(MODULES_DIR)/cloud_protocol/my_http_client.c

AUTH_CONTROL_SRCS =

DEVICE_MONITOR_SRCS =

# 根据配置选择性编译的源文件
CONDITIONAL_SRCS =

# 根据云端协议类型添加对应源文件
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
CONDITIONAL_SRCS += $(CLOUD_PROTOCOL_SRCS)
endif

ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
CONDITIONAL_SRCS += $(CLOUD_PROTOCOL_SRCS)
endif

# 根据认证类型添加对应源文件
ifeq ($(QRZL_AUTH_ONE_LINK_HTTP),yes)
CONDITIONAL_SRCS += $(AUTH_CONTROL_SRCS)
endif

ifeq ($(QRZL_AUTH_CMP_HTTP),yes)
CONDITIONAL_SRCS += $(AUTH_CONTROL_SRCS)
endif

# 所有源文件
ALL_SRCS = $(CORE_SRCS) $(COMMON_UTILS_SRCS) $(CONDITIONAL_SRCS)

# 目标文件
OBJS = $(ALL_SRCS:.c=.o)

# 头文件搜索路径
CFLAGS += -I$(INC_DIR)
CFLAGS += -I$(INC_DIR)/modules
CFLAGS += -I$(COMMON_UTILS_DIR)
CFLAGS += -I$(APP_DIR)/include
CFLAGS += -I$(zte_lib_path)/libsoftap
CFLAGS += -I$(zte_lib_path)/libsoft_timer
CFLAGS += -I$(zte_lib_path)/libatutils
CFLAGS += -I$(zte_lib_path)/libnvram
CFLAGS += -I$(zte_lib_path)/libcurl/install/include
CFLAGS += -I$(zte_lib_path)/libpahomqttc/install/include
CFLAGS += -I$(zte_lib_path)/libwolfssl/install/include

# 编译选项
CFLAGS += -Wall -Wextra -Werror
CFLAGS += -std=c99
CFLAGS += -D_GNU_SOURCE
CFLAGS += -DQRZL_VERSION=\"$(VERSION)\"
CFLAGS += $(CUSTOM_MACRO)
# 忽略宏重定义错误（由于系统头文件冲突）
CFLAGS += -Wno-error

# 根据配置启用HTTPS支持
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
CFLAGS += -DQRZL_ENABLE_HTTPS
endif

# 根据配置启用MQTT支持
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
CFLAGS += -DQRZL_ENABLE_MQTT
endif

# 启用wolfSSL支持
ifdef QRZL_ENABLE_WOLFSSL
CFLAGS += -DQRZL_ENABLE_WOLFSSL
endif

# 调试和优化选项
ifeq ($(GLOBAL_DEBUG),yes)
CFLAGS += -g -O0 -DDEBUG
else
CFLAGS += -Os -DNDEBUG
endif

# 代码优化选项
CFLAGS += -ffunction-sections -fdata-sections
CFLAGS += -fstack-protector-strong

# 链接选项
LDFLAGS += -Wl,--as-needed -Wl,--gc-sections
LDFLAGS += -Wl,--strip-all

# 库文件
LDLIBS = -lpthread -lm -lrt

# ZTE库文件
LDLIBS += -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS += -lsoft_timer -L$(zte_lib_path)/libsoft_timer
LDLIBS += -latutils -L$(zte_lib_path)/libatutils
LDLIBS += -lnvram -L$(zte_lib_path)/libnvram

# 条件链接HTTPS库
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_HTTP)
LDLIBS += -lcurl -L$(zte_lib_path)/libcurl/install/lib/
# 总是链接WolfSSL库，因为curl需要它
LDLIBS += -lwolfssl -L$(zte_lib_path)/libwolfssl/install/lib/
endif

# 条件链接MQTT库
ifeq ($(QRZL_CLOUD_PROTOCOL),QRZL_CLOUD_MQTT)
LDLIBS += -lpaho-mqtt3c -L$(zte_lib_path)/libpahomqttc/install/lib/
endif

#*******************************************************************************
# 构建目标
#*******************************************************************************

.PHONY: all clean install romfs help info

all: $(EXEC)

$(EXEC): $(OBJS)
	@echo "Linking $(EXEC)..."
	$(CC) $(LDFLAGS) -o $@ $^ $(LDLIBS)
	@cp $@ $@.elf
	@echo "Build completed: $(EXEC)"

# 编译规则
%.o: %.c
	@echo "Compiling $<..."
	$(CC) $(CFLAGS) -c $< -o $@

# 创建必要的目录
$(MODULES_DIR)/cloud_protocol:
	@mkdir -p $@

$(MODULES_DIR)/auth_control:
	@mkdir -p $@

$(MODULES_DIR)/device_monitor:
	@mkdir -p $@

# 安装到rootfs
romfs: $(EXEC)
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)
ifeq ($(QRZL_USERDATA_OTA),yes)
	@mkdir -p $(PRJ_BIN_DIR)/../../fs/normal/qrzl_ota/bin/
	@cp -f $(EXEC) $(PRJ_BIN_DIR)/../../fs/normal/qrzl_ota/bin/
endif

# 清理
clean:
	@echo "Cleaning..."
	@rm -f $(EXEC) *.elf *.gdb
	@find . -name "*.o" -delete
	@echo "Clean completed"

# 深度清理
distclean: clean
	@rm -rf $(MODULES_DIR)

# 显示构建信息
info:
	@echo "QRZL App Refactored Build Information"
	@echo "====================================="
	@echo "Version: $(VERSION)"
	@echo "Target: $(EXEC)"
	@echo "Cloud Protocol: $(QRZL_CLOUD_PROTOCOL)"
	@echo "HTTP Request Type: $(QRZL_CLOUD_HTTP_REQUEST_TYPE)"
	@echo "MQTT Type: $(QRZL_CLOUD_MQTT_TYPE)"
	@echo "Auth Customer: $(QRZL_AUTH_CUSTOMER)"
	@echo "OneLink Auth: $(QRZL_AUTH_ONE_LINK_HTTP)"
	@echo "CMP Auth: $(QRZL_AUTH_CMP_HTTP)"
	@echo "Debug Mode: $(GLOBAL_DEBUG)"
	@echo "Source Files: $(words $(ALL_SRCS)) files"
	@echo "Object Files: $(words $(OBJS)) files"

# 帮助信息
help:
	@echo "QRZL App Refactored Makefile Help"
	@echo "================================="
	@echo "Available targets:"
	@echo "  all      - Build the application (default)"
	@echo "  clean    - Remove object files and executable"
	@echo "  distclean- Deep clean including generated directories"
	@echo "  romfs    - Install to rootfs"
	@echo "  info     - Show build information"
	@echo "  help     - Show this help message"
	@echo ""
	@echo "Build variables:"
	@echo "  GLOBAL_DEBUG=yes     - Enable debug build"
	@echo "  QRZL_CLOUD_PROTOCOL  - Set cloud protocol type"
	@echo "  QRZL_AUTH_CUSTOMER   - Set authentication customer type"

# 依赖关系
$(SRC_DIR)/qrzl_main.o: $(INC_DIR)/qrzl_common.h $(INC_DIR)/qrzl_timer.h $(INC_DIR)/qrzl_thread_mgr.h $(INC_DIR)/qrzl_signal_handler.h
$(SRC_DIR)/qrzl_timer.o: $(INC_DIR)/qrzl_timer.h $(INC_DIR)/qrzl_common.h
$(SRC_DIR)/qrzl_thread_mgr.o: $(INC_DIR)/qrzl_thread_mgr.h $(INC_DIR)/qrzl_common.h
$(SRC_DIR)/qrzl_signal_handler.o: $(INC_DIR)/qrzl_signal_handler.h $(INC_DIR)/qrzl_common.h
$(SRC_DIR)/qrzl_network_client.o: $(INC_DIR)/qrzl_network_client.h $(INC_DIR)/qrzl_common.h
