# QRZL App 重构方案

## 重构目标

1. 解决现有架构问题：过度使用sleep、缺乏信号处理、目录结构混乱
2. 参考 zte_comm 架构，实现模块化设计
3. 引入定时器系统替代sleep调用
4. 统一网络通信模块，减少代码重复
5. 添加优雅的资源管理和信号处理

## 新的目录结构

```
qrzl_app_refactored/
├── inc/                          # 头文件目录
│   ├── qrzl_common.h            # 公共定义和宏
│   ├── qrzl_timer.h             # 定时器系统
│   ├── qrzl_thread_mgr.h        # 线程管理器
│   ├── qrzl_signal_handler.h    # 信号处理
│   ├── qrzl_network_client.h    # 统一网络客户端
│   ├── qrzl_device_control.h    # 设备控制
│   └── modules/                 # 各模块头文件
│       ├── cloud_protocol.h     # 云端协议接口
│       ├── auth_control.h       # 认证控制接口
│       └── device_monitor.h     # 设备监控接口
├── src/                         # 源文件目录
│   ├── qrzl_main.c             # 主程序
│   ├── qrzl_timer.c            # 定时器系统实现
│   ├── qrzl_thread_mgr.c       # 线程管理器实现
│   ├── qrzl_signal_handler.c   # 信号处理实现
│   ├── qrzl_network_client.c   # 统一网络客户端实现
│   ├── qrzl_device_control.c   # 设备控制实现
│   ├── qrzl_utils.c            # 工具函数
│   └── modules/                # 各模块实现
│       ├── cloud_protocol/     # 云端协议模块
│       │   ├── http_protocol.c
│       │   ├── mqtt_protocol.c
│       │   └── tcp_protocol.c
│       ├── auth_control/       # 认证控制模块
│       │   ├── onelink_auth.c
│       │   ├── cmp_auth.c
│       │   └── customer_auth.c
│       └── device_monitor/     # 设备监控模块
│           ├── network_monitor.c
│           ├── battery_monitor.c
│           └── sim_monitor.c
├── common_utils/               # 公共工具库
│   ├── cjson.c
│   ├── http_client.c
│   └── md5.c
└── Makefile                    # 新的构建文件
```

## 核心改进

### 1. 定时器系统 (替代sleep)
- 基于事件驱动的定时器
- 支持一次性和周期性定时器
- 非阻塞式设计

### 2. 线程管理器
- 统一的线程创建和管理
- 线程池概念
- 优雅的线程退出机制

### 3. 信号处理
- 正确处理 SIGINT, SIGTERM 等信号
- 资源清理机制
- 优雅退出流程

### 4. 统一网络客户端
- HTTP/HTTPS/MQTT 统一接口
- 连接池管理
- 自动重连机制

### 5. 模块化设计
- 清晰的模块边界
- 标准化的接口定义
- 易于扩展和维护

## 编译和使用

```bash
cd /home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build
make app RF_TYPE=230A DCXO=yes
```

## 性能优化

1. 减少线程阻塞时间
2. 优化网络请求频率
3. 改善内存使用效率
4. 提高响应速度
