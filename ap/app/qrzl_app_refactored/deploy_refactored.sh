#!/bin/bash

#*******************************************************************************
# QRZL App 重构版本部署脚本
# 用于将重构版本替换原有的qrzl_app构建
#*******************************************************************************

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
QRZL_APP_DIR="$(dirname "$SCRIPT_DIR")/qrzl_app"
REFACTORED_DIR="$SCRIPT_DIR"
CONFIG_MK="/home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build/config.mk"

echo "=== QRZL App 重构版本部署脚本 ==="
echo "原版本目录: $QRZL_APP_DIR"
echo "重构版本目录: $REFACTORED_DIR"

# 检查config.mk配置
echo "0. 检查config.mk配置..."
if [ -f "$CONFIG_MK" ]; then
    # 检查云端协议配置
    CLOUD_PROTOCOL=$(grep "^export QRZL_CLOUD_PROTOCOL" "$CONFIG_MK" | cut -d'=' -f2 | tr -d ' ')
    HTTP_REQUEST_TYPE=$(grep "^export QRZL_CLOUD_HTTP_REQUEST_TYPE" "$CONFIG_MK" | cut -d'=' -f2 | tr -d ' ')

    echo "   云端协议: $CLOUD_PROTOCOL"
    echo "   HTTP请求类型: $HTTP_REQUEST_TYPE"

    if [ "$CLOUD_PROTOCOL" = "QRZL_CLOUD_HTTP" ] && [ "$HTTP_REQUEST_TYPE" = "MY" ]; then
        echo "   ✅ 配置正确，支持MY客户的XUNJI协议"
    else
        echo "   ⚠️  警告: 当前配置可能不支持XUNJI协议"
        echo "      建议配置: QRZL_CLOUD_PROTOCOL := QRZL_CLOUD_HTTP"
        echo "      建议配置: QRZL_CLOUD_HTTP_REQUEST_TYPE := MY"
    fi
else
    echo "   ❌ 错误: 找不到config.mk文件: $CONFIG_MK"
    exit 1
fi

# 检查目录是否存在
if [ ! -d "$QRZL_APP_DIR" ]; then
    echo "错误: 原版本目录不存在: $QRZL_APP_DIR"
    exit 1
fi

if [ ! -d "$REFACTORED_DIR" ]; then
    echo "错误: 重构版本目录不存在: $REFACTORED_DIR"
    exit 1
fi

# 备份原有的Makefile
echo "1. 备份原有的Makefile..."
if [ -f "$QRZL_APP_DIR/Makefile" ] && [ ! -f "$QRZL_APP_DIR/Makefile.original" ]; then
    cp "$QRZL_APP_DIR/Makefile" "$QRZL_APP_DIR/Makefile.original"
    echo "   已备份: Makefile -> Makefile.original"
fi

# 替换Makefile
echo "2. 部署重构版本的Makefile..."
cp "$REFACTORED_DIR/Makefile.production" "$QRZL_APP_DIR/Makefile"
echo "   已替换: $QRZL_APP_DIR/Makefile"

# 创建符号链接指向重构版本的源代码
echo "3. 创建源代码符号链接..."
if [ ! -L "$QRZL_APP_DIR/qrzl_app_refactored" ]; then
    ln -sf "$REFACTORED_DIR" "$QRZL_APP_DIR/qrzl_app_refactored"
    echo "   已创建符号链接: $QRZL_APP_DIR/qrzl_app_refactored -> $REFACTORED_DIR"
fi

echo "=== 部署完成 ==="
echo ""
echo "现在可以使用以下命令编译重构版本:"
echo "make -C /home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build app RF_TYPE=230A DCXO=yes"
echo ""
echo "测试编译:"
echo "正在测试编译..."
if make -C /home/<USER>/work/Code-u28/ap/project/zx297520v3/prj_mifi_mz804_my/build app RF_TYPE=230A DCXO=yes; then
    echo "✅ 编译成功！重构版本已准备就绪"
else
    echo "❌ 编译失败，请检查错误信息"
fi
echo ""
echo "如需恢复原版本，请运行:"
echo "$SCRIPT_DIR/restore_original.sh"
