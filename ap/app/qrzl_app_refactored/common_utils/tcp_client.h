#ifndef TCP_CLIENT_H
#define TCP_CLIENT_H

#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* TCP客户端返回值定义 */
#define TCP_OK            0
#define TCP_ERROR        -1
#define TCP_TIMEOUT      -2
#define TCP_DISCONNECTED -3

/* TCP客户端配置 */
#define TCP_MAX_MSG_SIZE     4096
#define TCP_CONNECT_TIMEOUT  10   /* 连接超时(秒) */
#define TCP_RECV_TIMEOUT     30   /* 接收超时(秒) */

/* TCP客户端句柄 */
typedef struct tcp_client_t* tcp_client_handle_t;

/* TCP消息回调函数类型 */
typedef void (*tcp_message_callback_t)(const unsigned char *data, size_t len, void *user_data);

/* TCP连接状态回调函数类型 */
typedef void (*tcp_connection_callback_t)(int connected, void *user_data);

/**
 * 创建TCP客户端
 * @param hostname 服务器主机名或IP
 * @param port 服务器端口
 * @return TCP客户端句柄，失败返回NULL
 */
tcp_client_handle_t tcp_client_create(const char *hostname, int port);

/**
 * 销毁TCP客户端
 * @param client TCP客户端句柄
 */
void tcp_client_destroy(tcp_client_handle_t client);

/**
 * 连接到服务器
 * @param client TCP客户端句柄
 * @return TCP_OK成功，其他值失败
 */
int tcp_client_connect(tcp_client_handle_t client);

/**
 * 断开连接
 * @param client TCP客户端句柄
 */
void tcp_client_disconnect(tcp_client_handle_t client);

/**
 * 检查连接状态
 * @param client TCP客户端句柄
 * @return 1已连接，0未连接
 */
int tcp_client_is_connected(tcp_client_handle_t client);

/**
 * 发送数据
 * @param client TCP客户端句柄
 * @param data 要发送的数据
 * @param len 数据长度
 * @return 实际发送的字节数，失败返回TCP_ERROR
 */
int tcp_client_send(tcp_client_handle_t client, const unsigned char *data, size_t len);

/**
 * 接收数据
 * @param client TCP客户端句柄
 * @param buffer 接收缓冲区
 * @param buffer_size 缓冲区大小
 * @param timeout_ms 超时时间(毫秒)，0表示阻塞
 * @return 实际接收的字节数，失败返回TCP_ERROR，超时返回TCP_TIMEOUT
 */
int tcp_client_recv(tcp_client_handle_t client, unsigned char *buffer, size_t buffer_size, int timeout_ms);

/**
 * 设置消息回调函数
 * @param client TCP客户端句柄
 * @param callback 消息回调函数
 * @param user_data 用户数据
 */
void tcp_client_set_message_callback(tcp_client_handle_t client, tcp_message_callback_t callback, void *user_data);

/**
 * 设置连接状态回调函数
 * @param client TCP客户端句柄
 * @param callback 连接状态回调函数
 * @param user_data 用户数据
 */
void tcp_client_set_connection_callback(tcp_client_handle_t client, tcp_connection_callback_t callback, void *user_data);

/**
 * 启动TCP客户端接收线程
 * @param client TCP客户端句柄
 * @return TCP_OK成功，其他值失败
 */
int tcp_client_start_recv_thread(tcp_client_handle_t client);

/**
 * 停止TCP客户端接收线程
 * @param client TCP客户端句柄
 */
void tcp_client_stop_recv_thread(tcp_client_handle_t client);

/**
 * 设置TCP选项
 * @param client TCP客户端句柄
 * @param keep_alive 是否启用keep-alive
 * @param nodelay 是否启用TCP_NODELAY
 * @return TCP_OK成功，其他值失败
 */
int tcp_client_set_options(tcp_client_handle_t client, int keep_alive, int nodelay);

/**
 * 获取连接信息
 * @param client TCP客户端句柄
 * @param local_ip 本地IP地址缓冲区
 * @param local_port 本地端口
 * @param remote_ip 远程IP地址缓冲区
 * @param remote_port 远程端口
 * @param ip_buffer_size IP地址缓冲区大小
 * @return TCP_OK成功，其他值失败
 */
int tcp_client_get_connection_info(tcp_client_handle_t client, 
                                  char *local_ip, int *local_port,
                                  char *remote_ip, int *remote_port,
                                  size_t ip_buffer_size);

#ifdef __cplusplus
}
#endif

#endif /* TCP_CLIENT_H */
