/* 避免宏定义冲突，先包含系统头文件 */
#include <netinet/in.h>
#include <arpa/inet.h>

/* 避免宏重定义警告 */
#ifdef _POSIX_C_SOURCE
#undef _POSIX_C_SOURCE
#endif
#ifdef _GNU_SOURCE
#undef _GNU_SOURCE
#endif

#include "http_client.h"
#include "../inc/qrzl_common.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* 默认支持HTTP，QRZL_ENABLE_HTTPS宏控制HTTPS支持 */
#include <curl/curl.h>
#ifdef QRZL_ENABLE_HTTPS
#ifdef QRZL_ENABLE_WOLFSSL
#include <wolfssl/options.h>
#include <wolfssl/ssl.h>
#endif
#endif

/* HTTP客户端 - 提供4个标准接口: http_get, http_post, https_get, https_post */

/* HTTP响应数据结构 */
typedef struct {
    char *data;
    size_t size;
    size_t capacity;
} http_response_t;

/* HTTP客户端全局状态 */
static int http_client_initialized = 0;

/* HTTP客户端初始化 */
static int http_client_init(void) {
    if (http_client_initialized) {
        return HTTP_OK;
    }

    if (curl_global_init(CURL_GLOBAL_DEFAULT) != CURLE_OK) {
        qrzl_log_error("Failed to initialize libcurl");
        return HTTP_ERROR;
    }

    http_client_initialized = 1;
    qrzl_log_info("HTTP client initialized");
    return HTTP_OK;
}

/* HTTP客户端清理 */
static void http_client_cleanup(void) {
    if (!http_client_initialized) {
        return;
    }

    curl_global_cleanup();

    http_client_initialized = 0;
    qrzl_log_info("HTTP client cleaned up");
}

/* 写入回调函数 */
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t total_size = size * nmemb;
    http_response_t *response = (http_response_t *)userp;

    /* 检查是否需要扩展缓冲区 */
    if (response->size + total_size >= response->capacity) {
        size_t new_capacity = response->capacity * 2;
        if (new_capacity < response->size + total_size + 1) {
            new_capacity = response->size + total_size + 1;
        }

        char *new_data = realloc(response->data, new_capacity);
        if (!new_data) {
            qrzl_log_error("Failed to reallocate response buffer");
            return 0;
        }

        response->data = new_data;
        response->capacity = new_capacity;
    }

    /* 复制数据 */
    memcpy(response->data + response->size, contents, total_size);
    response->size += total_size;
    response->data[response->size] = '\0';

    return total_size;
}

/* 通用HTTP请求函数 */
static int http_request_internal(const char *url, const char *method, const char *body, 
                               char *response, size_t resp_size, int use_ssl) {
    if (!url || !response || resp_size == 0) {
        qrzl_log_error("Invalid parameters for HTTP request");
        return HTTP_ERROR;
    }
    
    /* 确保HTTP客户端已初始化 */
    if (http_client_init() != HTTP_OK) {
        return HTTP_ERROR;
    }
    CURL *curl;
    CURLcode res;
    
    curl = curl_easy_init();
    if (!curl) {
        qrzl_log_error("Failed to initialize curl handle");
        return HTTP_ERROR;
    }
    
    /* 设置URL */
    curl_easy_setopt(curl, CURLOPT_URL, url);
    
    /* 设置请求方法 */
    if (strcmp(method, "POST") == 0) {
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        if (body) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
        }
    } else if (strcmp(method, "GET") == 0) {
        curl_easy_setopt(curl, CURLOPT_HTTPGET, 1L);
    }
    
    /* SSL/TLS设置 - 只有启用HTTPS时才设置 */
#ifdef QRZL_ENABLE_HTTPS
    if (use_ssl) {
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    }
#else
    if (use_ssl) {
        qrzl_log_error("HTTPS not supported - QRZL_ENABLE_HTTPS not defined");
        curl_easy_cleanup(curl);
        return HTTP_ERROR;
    }
#endif
    
    /* 设置HTTP头 */
    struct curl_slist *headers = NULL;
    if (body && strlen(body) > 0) {
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    }
    
    /* 设置超时 */
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, HTTP_CONNECT_TIMEOUT);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, HTTP_REQUEST_TIMEOUT);
    
    /* 设置响应回调 */
    http_response_t resp = {0};
    resp.data = malloc(1024);
    resp.capacity = 1024;
    if (!resp.data) {
        if (headers) curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        return HTTP_ERROR;
    }
    
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &resp);
    
    /* 执行请求 */
    qrzl_log_debug("%s request to: %s", method, url);
    if (body) {
        qrzl_log_debug("Request body: %s", body);
    }
    
    res = curl_easy_perform(curl);
    
    /* 处理结果 */
    int result = HTTP_ERROR;
    if (res == CURLE_OK) {
        if (resp.size > 0 && resp.size < resp_size) {
            strncpy(response, resp.data, resp.size);
            response[resp.size] = '\0';
            qrzl_log_debug("Response: %s", response);
            result = HTTP_OK;
        } else if (resp.size == 0) {
            response[0] = '\0';
            result = HTTP_OK;
        } else {
            qrzl_log_error("Response too large: %zu bytes", resp.size);
        }
    } else {
        qrzl_log_error("HTTP request failed: %s", curl_easy_strerror(res));
    }
    
    /* 清理资源 */
    free(resp.data);
    if (headers) curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    
    return result;
}

/* ==================== 4个标准接口实现 ==================== */

/* HTTP GET请求 */
int http_get(const char *url, char *response, size_t resp_size) {
    return http_request_internal(url, "GET", NULL, response, resp_size, 0);
}

/* HTTP POST请求 */
int http_post(const char *url, const char *body, char *response, size_t resp_size) {
    return http_request_internal(url, "POST", body, response, resp_size, 0);
}

/* HTTPS GET请求 */
int https_get(const char *url, char *response, size_t resp_size) {
    return http_request_internal(url, "GET", NULL, response, resp_size, 1);
}

/* HTTPS POST请求 */
int https_post(const char *url, const char *body, char *response, size_t resp_size) {
    return http_request_internal(url, "POST", body, response, resp_size, 1);
}

/* ==================== 兼容性接口 ==================== */

/* 兼容原有的http_send_post_request接口 */
int http_send_post_request(const char *url, const char *body, char *response, size_t response_size) {
    /* 根据URL协议自动选择HTTP或HTTPS */
    if (strncmp(url, "https://", 8) == 0) {
        return https_post(url, body, response, response_size);
    } else {
        return http_post(url, body, response, response_size);
    }
}

/* 兼容原有的https_send_post_request接口 */
int https_send_post_request(const char *url, const char *body, char *response, size_t response_size) {
    return https_post(url, body, response, response_size);
}
