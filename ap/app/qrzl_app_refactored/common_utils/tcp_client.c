/* 避免宏定义冲突，先包含系统头文件 */
#include <netinet/in.h>
#include <arpa/inet.h>

/* 避免宏重定义警告 */
#ifdef _POSIX_C_SOURCE
#undef _POSIX_C_SOURCE
#endif
#ifdef _GNU_SOURCE
#undef _GNU_SOURCE
#endif

#include "tcp_client.h"
#include "../inc/qrzl_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/tcp.h>
#include <netdb.h>
#include <errno.h>
#include <pthread.h>
#include <sys/select.h>
#include <fcntl.h>
#include <sys/types.h>

/* TCP客户端结构体 */
struct tcp_client_t {
    char hostname[256];
    int port;
    int sockfd;
    int connected;
    
    /* 回调函数 */
    tcp_message_callback_t message_callback;
    void *message_user_data;
    tcp_connection_callback_t connection_callback;
    void *connection_user_data;
    
    /* 接收线程 */
    pthread_t recv_thread;
    int recv_thread_running;
    pthread_mutex_t mutex;
};

/* 接收线程函数 */
static void *tcp_recv_thread_func(void *arg) {
    tcp_client_handle_t client = (tcp_client_handle_t)arg;
    unsigned char buffer[TCP_MAX_MSG_SIZE];
    int recv_len;
    
    qrzl_log_info("TCP receive thread started");
    
    while (client->recv_thread_running) {
        if (!client->connected) {
            usleep(100000); /* 100ms */
            continue;
        }
        
        recv_len = tcp_client_recv(client, buffer, sizeof(buffer), 1000); /* 1秒超时 */
        if (recv_len > 0) {
            if (client->message_callback) {
                client->message_callback(buffer, recv_len, client->message_user_data);
            }
        } else if (recv_len == TCP_DISCONNECTED) {
            qrzl_log_warn("TCP connection lost in receive thread");
            pthread_mutex_lock(&client->mutex);
            client->connected = 0;
            if (client->connection_callback) {
                client->connection_callback(0, client->connection_user_data);
            }
            pthread_mutex_unlock(&client->mutex);
        }
    }
    
    qrzl_log_info("TCP receive thread stopped");
    return NULL;
}

/* 创建TCP客户端 */
tcp_client_handle_t tcp_client_create(const char *hostname, int port) {
    if (!hostname || port <= 0 || port > 65535) {
        qrzl_log_error("Invalid parameters for TCP client creation");
        return NULL;
    }
    
    tcp_client_handle_t client = malloc(sizeof(struct tcp_client_t));
    if (!client) {
        qrzl_log_error("Failed to allocate memory for TCP client");
        return NULL;
    }
    
    memset(client, 0, sizeof(struct tcp_client_t));
    strncpy(client->hostname, hostname, sizeof(client->hostname) - 1);
    client->port = port;
    client->sockfd = -1;
    client->connected = 0;
    client->recv_thread_running = 0;
    
    if (pthread_mutex_init(&client->mutex, NULL) != 0) {
        qrzl_log_error("Failed to initialize TCP client mutex");
        free(client);
        return NULL;
    }
    
    qrzl_log_info("TCP client created for %s:%d", hostname, port);
    return client;
}

/* 销毁TCP客户端 */
void tcp_client_destroy(tcp_client_handle_t client) {
    if (!client) {
        return;
    }
    
    tcp_client_stop_recv_thread(client);
    tcp_client_disconnect(client);
    pthread_mutex_destroy(&client->mutex);
    free(client);
    qrzl_log_info("TCP client destroyed");
}

/* 连接到服务器 */
int tcp_client_connect(tcp_client_handle_t client) {
    if (!client) {
        return TCP_ERROR;
    }
    
    if (client->connected) {
        return TCP_OK;
    }
    
    /* 使用简单的socket连接，避免getaddrinfo */
    struct sockaddr_in server_addr;
    struct hostent *host_entry;

    qrzl_log_info("Resolving hostname: %s:%d", client->hostname, client->port);

    /* 解析主机名 */
    host_entry = gethostbyname(client->hostname);
    if (host_entry == NULL) {
        qrzl_log_error("Failed to resolve hostname: %s", client->hostname);
        return TCP_ERROR;
    }

    /* 创建socket */
    client->sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (client->sockfd == -1) {
        qrzl_log_error("Failed to create socket: %s", strerror(errno));
        return TCP_ERROR;
    }

    /* 设置连接超时 */
    struct timeval timeout;
    timeout.tv_sec = TCP_CONNECT_TIMEOUT;
    timeout.tv_usec = 0;
    setsockopt(client->sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
    setsockopt(client->sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));

    /* 设置服务器地址 */
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(client->port);
    memcpy(&server_addr.sin_addr, host_entry->h_addr_list[0], host_entry->h_length);

    qrzl_log_info("Connecting to %s:%d", client->hostname, client->port);

    /* 连接服务器 */
    if (connect(client->sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) == -1) {
        qrzl_log_error("Failed to connect: %s", strerror(errno));
        close(client->sockfd);
        client->sockfd = -1;
        return TCP_ERROR;
    }
    
    pthread_mutex_lock(&client->mutex);
    client->connected = 1;
    if (client->connection_callback) {
        client->connection_callback(1, client->connection_user_data);
    }
    pthread_mutex_unlock(&client->mutex);
    
    qrzl_log_info("TCP connected successfully");
    return TCP_OK;
}

/* 断开连接 */
void tcp_client_disconnect(tcp_client_handle_t client) {
    if (!client) {
        return;
    }
    
    pthread_mutex_lock(&client->mutex);
    if (client->connected) {
        client->connected = 0;
        if (client->sockfd != -1) {
            close(client->sockfd);
            client->sockfd = -1;
        }
        if (client->connection_callback) {
            client->connection_callback(0, client->connection_user_data);
        }
        qrzl_log_info("TCP disconnected");
    }
    pthread_mutex_unlock(&client->mutex);
}

/* 检查连接状态 */
int tcp_client_is_connected(tcp_client_handle_t client) {
    if (!client) {
        return 0;
    }
    return client->connected;
}

/* 发送数据 */
int tcp_client_send(tcp_client_handle_t client, const unsigned char *data, size_t len) {
    if (!client || !data || len == 0) {
        return TCP_ERROR;
    }
    
    if (!client->connected || client->sockfd == -1) {
        qrzl_log_error("TCP not connected");
        return TCP_ERROR;
    }
    
    int sent = send(client->sockfd, data, len, MSG_NOSIGNAL);
    if (sent == -1) {
        qrzl_log_error("TCP send failed: %s", strerror(errno));
        tcp_client_disconnect(client);
        return TCP_ERROR;
    }
    
    qrzl_log_debug("TCP sent %d bytes", sent);
    return sent;
}

/* 接收数据 */
int tcp_client_recv(tcp_client_handle_t client, unsigned char *buffer, size_t buffer_size, int timeout_ms) {
    if (!client || !buffer || buffer_size == 0) {
        return TCP_ERROR;
    }
    
    if (!client->connected || client->sockfd == -1) {
        return TCP_DISCONNECTED;
    }
    
    if (timeout_ms > 0) {
        fd_set readfds;
        struct timeval timeout;
        
        FD_ZERO(&readfds);
        FD_SET(client->sockfd, &readfds);
        
        timeout.tv_sec = timeout_ms / 1000;
        timeout.tv_usec = (timeout_ms % 1000) * 1000;
        
        int select_result = select(client->sockfd + 1, &readfds, NULL, NULL, &timeout);
        if (select_result == 0) {
            return TCP_TIMEOUT;
        } else if (select_result == -1) {
            qrzl_log_error("TCP select failed: %s", strerror(errno));
            return TCP_ERROR;
        }
    }
    
    int received = recv(client->sockfd, buffer, buffer_size, 0);
    if (received == 0) {
        qrzl_log_warn("TCP connection closed by peer");
        tcp_client_disconnect(client);
        return TCP_DISCONNECTED;
    } else if (received == -1) {
        if (errno == EAGAIN || errno == EWOULDBLOCK) {
            return TCP_TIMEOUT;
        }
        qrzl_log_error("TCP recv failed: %s", strerror(errno));
        tcp_client_disconnect(client);
        return TCP_ERROR;
    }
    
    qrzl_log_debug("TCP received %d bytes", received);
    return received;
}

/* 设置消息回调函数 */
void tcp_client_set_message_callback(tcp_client_handle_t client, tcp_message_callback_t callback, void *user_data) {
    if (!client) {
        return;
    }

    pthread_mutex_lock(&client->mutex);
    client->message_callback = callback;
    client->message_user_data = user_data;
    pthread_mutex_unlock(&client->mutex);
}

/* 设置连接状态回调函数 */
void tcp_client_set_connection_callback(tcp_client_handle_t client, tcp_connection_callback_t callback, void *user_data) {
    if (!client) {
        return;
    }

    pthread_mutex_lock(&client->mutex);
    client->connection_callback = callback;
    client->connection_user_data = user_data;
    pthread_mutex_unlock(&client->mutex);
}

/* 启动TCP客户端接收线程 */
int tcp_client_start_recv_thread(tcp_client_handle_t client) {
    if (!client) {
        return TCP_ERROR;
    }

    if (client->recv_thread_running) {
        return TCP_OK;
    }

    client->recv_thread_running = 1;

    if (pthread_create(&client->recv_thread, NULL, tcp_recv_thread_func, client) != 0) {
        qrzl_log_error("Failed to create TCP receive thread");
        client->recv_thread_running = 0;
        return TCP_ERROR;
    }

    qrzl_log_info("TCP receive thread started");
    return TCP_OK;
}

/* 停止TCP客户端接收线程 */
void tcp_client_stop_recv_thread(tcp_client_handle_t client) {
    if (!client || !client->recv_thread_running) {
        return;
    }

    client->recv_thread_running = 0;

    if (pthread_join(client->recv_thread, NULL) != 0) {
        qrzl_log_error("Failed to join TCP receive thread");
    }

    qrzl_log_info("TCP receive thread stopped");
}

/* 设置TCP选项 */
int tcp_client_set_options(tcp_client_handle_t client, int keep_alive, int nodelay) {
    if (!client || client->sockfd == -1) {
        return TCP_ERROR;
    }

    if (keep_alive) {
        int optval = 1;
        if (setsockopt(client->sockfd, SOL_SOCKET, SO_KEEPALIVE, &optval, sizeof(optval)) == -1) {
            qrzl_log_error("Failed to set SO_KEEPALIVE: %s", strerror(errno));
            return TCP_ERROR;
        }
    }

    if (nodelay) {
        int optval = 1;
        if (setsockopt(client->sockfd, IPPROTO_TCP, TCP_NODELAY, &optval, sizeof(optval)) == -1) {
            qrzl_log_error("Failed to set TCP_NODELAY: %s", strerror(errno));
            return TCP_ERROR;
        }
    }

    return TCP_OK;
}

/* 获取连接信息 */
int tcp_client_get_connection_info(tcp_client_handle_t client,
                                  char *local_ip, int *local_port,
                                  char *remote_ip, int *remote_port,
                                  size_t ip_buffer_size) {
    if (!client || client->sockfd == -1) {
        return TCP_ERROR;
    }

    struct sockaddr_in local_addr, remote_addr;
    socklen_t addr_len = sizeof(struct sockaddr_in);

    if (getsockname(client->sockfd, (struct sockaddr*)&local_addr, &addr_len) == -1) {
        qrzl_log_error("Failed to get local address: %s", strerror(errno));
        return TCP_ERROR;
    }

    if (getpeername(client->sockfd, (struct sockaddr*)&remote_addr, &addr_len) == -1) {
        qrzl_log_error("Failed to get remote address: %s", strerror(errno));
        return TCP_ERROR;
    }

    if (local_ip) {
        strncpy(local_ip, inet_ntoa(local_addr.sin_addr), ip_buffer_size - 1);
        local_ip[ip_buffer_size - 1] = '\0';
    }

    if (local_port) {
        *local_port = ntohs(local_addr.sin_port);
    }

    if (remote_ip) {
        strncpy(remote_ip, inet_ntoa(remote_addr.sin_addr), ip_buffer_size - 1);
        remote_ip[ip_buffer_size - 1] = '\0';
    }

    if (remote_port) {
        *remote_port = ntohs(remote_addr.sin_port);
    }

    return TCP_OK;
}
