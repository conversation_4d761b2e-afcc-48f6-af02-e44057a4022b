#ifndef HTTP_CLIENT_H
#define HTTP_CLIENT_H

#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 默认缓冲区大小 */
#define HTTP_RESPONSE_MAX  8192
#define HTTP_REQUEST_MAX   4096

/* 返回值定义 */
#define HTTP_OK            0
#define HTTP_ERROR        -1

/* HTTP超时设置 */
#define HTTP_CONNECT_TIMEOUT  10  /* 连接超时(秒) */
#define HTTP_REQUEST_TIMEOUT  30  /* 请求超时(秒) */

/**
 * HTTP GET请求
 * @param url 请求URL
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_get(const char *url, char *response, size_t resp_size);

/**
 * HTTP POST请求 (content-type: application/json)
 * @param url 请求URL
 * @param body 请求体(JSON格式)
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_post(const char *url, const char *body, char *response, size_t resp_size);

/**
 * HTTP PUT请求
 * @param url 请求URL
 * @param body 请求体
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_put(const char *url, const char *body, char *response, size_t resp_size);

/**
 * HTTP DELETE请求
 * @param url 请求URL
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_delete(const char *url, char *response, size_t resp_size);

/**
 * HTTPS GET请求
 * @param url 请求URL (https://)
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int https_get(const char *url, char *response, size_t resp_size);

/**
 * HTTPS POST请求
 * @param url 请求URL (https://)
 * @param body 请求体(JSON格式)
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int https_post(const char *url, const char *body, char *response, size_t resp_size);

/**
 * 设置HTTP请求头
 * @param headers 头部字符串数组，以NULL结尾
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_set_headers(const char **headers);

/**
 * 设置HTTP用户代理
 * @param user_agent 用户代理字符串
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_set_user_agent(const char *user_agent);

/**
 * 设置HTTP超时时间
 * @param connect_timeout 连接超时(秒)
 * @param request_timeout 请求超时(秒)
 * @return HTTP_OK成功，HTTP_ERROR失败
 */
int http_set_timeout(int connect_timeout, int request_timeout);

/**
 * HTTP POST请求 - 兼容原有接口
 * @param url 请求URL
 * @param body 请求体
 * @param response 响应缓冲区
 * @param response_size 响应缓冲区大小
 * @return 0成功，-1失败
 */
int http_send_post_request(const char *url, const char *body, char *response, size_t response_size);

/**
 * HTTPS POST请求 - 支持SSL/TLS
 * @param url 请求URL (https://)
 * @param body 请求体
 * @param response 响应缓冲区
 * @param response_size 响应缓冲区大小
 * @return 0成功，-1失败
 */
int https_send_post_request(const char *url, const char *body, char *response, size_t response_size);

#ifdef __cplusplus
}
#endif

#endif // HTTP_CLIENT_H
