#include "mqtt_client.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#ifdef QRZL_ENABLE_MQTT
#include <MQTTClient.h>
#endif

#include "../inc/qrzl_common.h"

#ifdef QRZL_ENABLE_MQTT

/* MQTT客户端内部结构 */
typedef struct {
    MQTTClient client;
    mqtt_callbacks_t callbacks;
    char *client_id;
    bool connected;
    bool loop_running;
} mqtt_client_internal_t;

/* 连接丢失回调 */
static void mqtt_connection_lost(void *context, char *cause) {
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)context;
    
    qrzl_log_warn("MQTT connection lost: %s", cause ? cause : "unknown");
    internal->connected = false;
    
    if (internal->callbacks.on_disconnect) {
        internal->callbacks.on_disconnect(internal, MQTT_DISCONNECTED);
    }
}

/* 消息到达回调 */
static int mqtt_message_arrived(void *context, char *topicName, int topicLen, MQTTClient_message *message) {
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)context;
    
    if (internal->callbacks.on_message) {
        mqtt_message_t msg = {0};
        msg.topic = topicName;
        msg.payload = (char *)message->payload;
        msg.payload_len = message->payloadlen;
        msg.qos = (mqtt_qos_t)message->qos;
        msg.retained = message->retained;
        
        internal->callbacks.on_message(internal, &msg);
    }
    
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);
    return 1;
}

/* 消息发送完成回调 */
static void mqtt_delivery_complete(void *context, MQTTClient_deliveryToken token) {
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)context;
    
    if (internal->callbacks.on_publish) {
        internal->callbacks.on_publish(internal, token);
    }
}

/* 创建MQTT客户端 */
mqtt_client_t mqtt_client_create(const char *client_id, const mqtt_callbacks_t *callbacks) {
    if (!client_id) {
        qrzl_log_error("Invalid client_id");
        return NULL;
    }
    
    mqtt_client_internal_t *internal = QRZL_CALLOC(1, sizeof(mqtt_client_internal_t));
    if (!internal) {
        qrzl_log_error("Failed to allocate MQTT client memory");
        return NULL;
    }
    
    internal->client_id = strdup(client_id);
    if (!internal->client_id) {
        QRZL_FREE(internal);
        return NULL;
    }
    
    if (callbacks) {
        internal->callbacks = *callbacks;
    }
    
    qrzl_log_debug("MQTT client created: %s", client_id);
    return internal;
}

/* 销毁MQTT客户端 */
void mqtt_client_destroy(mqtt_client_t client) {
    if (!client) return;
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    if (internal->connected) {
        mqtt_disconnect(client);
    }
    
    if (internal->client) {
        MQTTClient_destroy(&internal->client);
    }
    
    QRZL_FREE(internal->client_id);
    QRZL_FREE(internal);
    
    qrzl_log_debug("MQTT client destroyed");
}

/* 连接到MQTT服务器 */
int mqtt_connect(mqtt_client_t client, const mqtt_connect_params_t *params) {
    if (!client || !params || !params->host) {
        return MQTT_ERROR;
    }
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    /* 构建服务器URI */
    char uri[256];
    const char *protocol = params->use_ssl ? "ssl" : "tcp";
    snprintf(uri, sizeof(uri), "%s://%s:%d", protocol, params->host, params->port);
    
    /* 创建MQTT客户端 */
    int rc = MQTTClient_create(&internal->client, uri, internal->client_id,
                              MQTTCLIENT_PERSISTENCE_NONE, NULL);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_log_error("Failed to create MQTT client: %d", rc);
        return MQTT_ERROR;
    }
    
    /* 设置回调函数 */
    MQTTClient_setCallbacks(internal->client, internal, mqtt_connection_lost,
                           mqtt_message_arrived, mqtt_delivery_complete);
    
    /* 设置连接选项 */
    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.keepAliveInterval = params->keepalive;
    conn_opts.cleansession = params->clean_session;
    
    if (params->username) {
        conn_opts.username = params->username;
    }
    if (params->password) {
        conn_opts.password = params->password;
    }
    
    /* 连接到服务器 */
    rc = MQTTClient_connect(internal->client, &conn_opts);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_log_error("Failed to connect to MQTT server: %d", rc);
        MQTTClient_destroy(&internal->client);
        return MQTT_ERROR;
    }
    
    internal->connected = true;
    qrzl_log_info("Connected to MQTT server: %s", uri);
    
    if (internal->callbacks.on_connect) {
        internal->callbacks.on_connect(internal, MQTT_OK);
    }
    
    return MQTT_OK;
}

/* 断开MQTT连接 */
int mqtt_disconnect(mqtt_client_t client) {
    if (!client) return MQTT_ERROR;
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    if (!internal->connected || !internal->client) {
        return MQTT_OK;
    }
    
    int rc = MQTTClient_disconnect(internal->client, 10000);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_log_warn("MQTT disconnect failed: %d", rc);
    }
    
    internal->connected = false;
    qrzl_log_info("Disconnected from MQTT server");
    
    if (internal->callbacks.on_disconnect) {
        internal->callbacks.on_disconnect(internal, MQTT_OK);
    }
    
    return MQTT_OK;
}

/* 发布消息 */
int mqtt_publish(mqtt_client_t client, const char *topic, const void *payload, 
                size_t payload_len, mqtt_qos_t qos, bool retained) {
    if (!client || !topic || !payload) {
        return MQTT_ERROR;
    }
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    if (!internal->connected) {
        qrzl_log_error("MQTT client not connected");
        return MQTT_DISCONNECTED;
    }
    
    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = (void *)payload;
    pubmsg.payloadlen = payload_len;
    pubmsg.qos = qos;
    pubmsg.retained = retained;
    
    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(internal->client, topic, &pubmsg, &token);
    
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_log_error("Failed to publish MQTT message: %d", rc);
        return MQTT_ERROR;
    }
    
    qrzl_log_debug("MQTT message published to %s, token: %d", topic, token);
    return token;
}

/* 订阅主题 */
int mqtt_subscribe(mqtt_client_t client, const char *topic, mqtt_qos_t qos) {
    if (!client || !topic) {
        return MQTT_ERROR;
    }
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    if (!internal->connected) {
        qrzl_log_error("MQTT client not connected");
        return MQTT_DISCONNECTED;
    }
    
    int rc = MQTTClient_subscribe(internal->client, topic, qos);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_log_error("Failed to subscribe to MQTT topic %s: %d", topic, rc);
        return MQTT_ERROR;
    }
    
    qrzl_log_debug("Subscribed to MQTT topic: %s", topic);
    return MQTT_OK;
}

/* 取消订阅主题 */
int mqtt_unsubscribe(mqtt_client_t client, const char *topic) {
    if (!client || !topic) {
        return MQTT_ERROR;
    }
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    if (!internal->connected) {
        qrzl_log_error("MQTT client not connected");
        return MQTT_DISCONNECTED;
    }
    
    int rc = MQTTClient_unsubscribe(internal->client, topic);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_log_error("Failed to unsubscribe from MQTT topic %s: %d", topic, rc);
        return MQTT_ERROR;
    }
    
    qrzl_log_debug("Unsubscribed from MQTT topic: %s", topic);
    return MQTT_OK;
}

/* 检查客户端是否已连接 */
bool mqtt_is_connected(mqtt_client_t client) {
    if (!client) return false;
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    return internal->connected && MQTTClient_isConnected(internal->client);
}

/* 处理网络事件(非阻塞) */
int mqtt_loop(mqtt_client_t client, int timeout_ms) {
    if (!client) return MQTT_ERROR;
    
    mqtt_client_internal_t *internal = (mqtt_client_internal_t *)client;
    
    if (!internal->connected) {
        return MQTT_DISCONNECTED;
    }
    
    /* Paho MQTT C客户端是同步的，这里只是检查连接状态 */
    if (!MQTTClient_isConnected(internal->client)) {
        internal->connected = false;
        return MQTT_DISCONNECTED;
    }
    
    return MQTT_OK;
}

#else
/* 不支持MQTT时的桩实现 */
mqtt_client_t mqtt_client_create(const char *client_id, const mqtt_callbacks_t *callbacks) {
    qrzl_log_warn("MQTT not supported (disabled)");
    return NULL;
}

void mqtt_client_destroy(mqtt_client_t client) {
    /* 空实现 */
}

int mqtt_connect(mqtt_client_t client, const mqtt_connect_params_t *params) {
    qrzl_log_warn("MQTT connect not supported (disabled)");
    return MQTT_ERROR;
}

int mqtt_disconnect(mqtt_client_t client) {
    return MQTT_OK;
}

int mqtt_publish(mqtt_client_t client, const char *topic, const void *payload, 
                size_t payload_len, mqtt_qos_t qos, bool retained) {
    qrzl_log_warn("MQTT publish not supported (disabled)");
    return MQTT_ERROR;
}

int mqtt_subscribe(mqtt_client_t client, const char *topic, mqtt_qos_t qos) {
    qrzl_log_warn("MQTT subscribe not supported (disabled)");
    return MQTT_ERROR;
}

int mqtt_unsubscribe(mqtt_client_t client, const char *topic) {
    qrzl_log_warn("MQTT unsubscribe not supported (disabled)");
    return MQTT_ERROR;
}

bool mqtt_is_connected(mqtt_client_t client) {
    return false;
}

int mqtt_loop(mqtt_client_t client, int timeout_ms) {
    return MQTT_ERROR;
}

#endif
