#ifndef MQTT_CLIENT_H
#define MQTT_CLIENT_H

#include <stddef.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MQTT返回值定义 */
#define MQTT_OK            0
#define MQTT_ERROR        -1
#define MQTT_TIMEOUT      -2
#define MQTT_DISCONNECTED -3

/* MQTT QoS级别 */
typedef enum {
    MQTT_QOS_0 = 0,  /* 最多一次 */
    MQTT_QOS_1 = 1,  /* 至少一次 */
    MQTT_QOS_2 = 2   /* 恰好一次 */
} mqtt_qos_t;

/* MQTT客户端句柄 */
typedef void* mqtt_client_t;

/* MQTT消息结构 */
typedef struct {
    char *topic;
    char *payload;
    size_t payload_len;
    mqtt_qos_t qos;
    bool retained;
} mqtt_message_t;

/* MQTT连接参数 */
typedef struct {
    const char *host;
    int port;
    const char *client_id;
    const char *username;
    const char *password;
    int keepalive;
    bool clean_session;
    bool use_ssl;
} mqtt_connect_params_t;

/* MQTT回调函数类型 */
typedef void (*mqtt_connect_callback_t)(mqtt_client_t client, int result);
typedef void (*mqtt_disconnect_callback_t)(mqtt_client_t client, int result);
typedef void (*mqtt_message_callback_t)(mqtt_client_t client, const mqtt_message_t *message);
typedef void (*mqtt_publish_callback_t)(mqtt_client_t client, int message_id);
typedef void (*mqtt_subscribe_callback_t)(mqtt_client_t client, int message_id, int *granted_qos);

/* MQTT回调函数结构 */
typedef struct {
    mqtt_connect_callback_t on_connect;
    mqtt_disconnect_callback_t on_disconnect;
    mqtt_message_callback_t on_message;
    mqtt_publish_callback_t on_publish;
    mqtt_subscribe_callback_t on_subscribe;
} mqtt_callbacks_t;

/**
 * 创建MQTT客户端
 * @param client_id 客户端ID
 * @param callbacks 回调函数结构
 * @return 客户端句柄，NULL表示失败
 */
mqtt_client_t mqtt_client_create(const char *client_id, const mqtt_callbacks_t *callbacks);

/**
 * 销毁MQTT客户端
 * @param client 客户端句柄
 */
void mqtt_client_destroy(mqtt_client_t client);

/**
 * 连接到MQTT服务器
 * @param client 客户端句柄
 * @param params 连接参数
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_connect(mqtt_client_t client, const mqtt_connect_params_t *params);

/**
 * 断开MQTT连接
 * @param client 客户端句柄
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_disconnect(mqtt_client_t client);

/**
 * 发布消息
 * @param client 客户端句柄
 * @param topic 主题
 * @param payload 消息内容
 * @param payload_len 消息长度
 * @param qos QoS级别
 * @param retained 是否保留消息
 * @return 消息ID，负数表示失败
 */
int mqtt_publish(mqtt_client_t client, const char *topic, const void *payload, 
                size_t payload_len, mqtt_qos_t qos, bool retained);

/**
 * 订阅主题
 * @param client 客户端句柄
 * @param topic 主题
 * @param qos QoS级别
 * @return 消息ID，负数表示失败
 */
int mqtt_subscribe(mqtt_client_t client, const char *topic, mqtt_qos_t qos);

/**
 * 取消订阅主题
 * @param client 客户端句柄
 * @param topic 主题
 * @return 消息ID，负数表示失败
 */
int mqtt_unsubscribe(mqtt_client_t client, const char *topic);

/**
 * 检查客户端是否已连接
 * @param client 客户端句柄
 * @return true已连接，false未连接
 */
bool mqtt_is_connected(mqtt_client_t client);

/**
 * 处理网络事件(非阻塞)
 * @param client 客户端句柄
 * @param timeout_ms 超时时间(毫秒)
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_loop(mqtt_client_t client, int timeout_ms);

/**
 * 启动网络循环线程
 * @param client 客户端句柄
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_loop_start(mqtt_client_t client);

/**
 * 停止网络循环线程
 * @param client 客户端句柄
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_loop_stop(mqtt_client_t client);

/**
 * 设置遗嘱消息
 * @param client 客户端句柄
 * @param topic 遗嘱主题
 * @param payload 遗嘱消息
 * @param payload_len 消息长度
 * @param qos QoS级别
 * @param retained 是否保留
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_will_set(mqtt_client_t client, const char *topic, const void *payload,
                 size_t payload_len, mqtt_qos_t qos, bool retained);

/**
 * 清除遗嘱消息
 * @param client 客户端句柄
 * @return MQTT_OK成功，其他值失败
 */
int mqtt_will_clear(mqtt_client_t client);

#ifdef __cplusplus
}
#endif

#endif // MQTT_CLIENT_H
