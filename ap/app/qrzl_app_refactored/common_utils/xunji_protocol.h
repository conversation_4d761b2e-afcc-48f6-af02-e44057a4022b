#ifndef XUNJI_PROTOCOL_H
#define XUNJI_PROTOCOL_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* XUNJI协议常量定义 */
#define XUNJI_FLAG_START_END        0x7E
#define XUNJI_ESCAPE_FLAG           0x7D
#define XUNJI_ESCAPE_7E             0x02
#define XUNJI_ESCAPE_7D             0x01

#define XUNJI_MAX_MSG_SIZE          4096
#define XUNJI_HEADER_SIZE           13
#define XUNJI_HEART_BEAT_MAX_TIMEOUT 3

/* XUNJI消息ID定义 */
#define XUNJI_MSG_TERMINAL_ACK      0x0001  /* 终端通用应答 */
#define XUNJI_MSG_HEARTBEAT         0x0002  /* 终端心跳 */
#define XUNJI_MSG_PLATFORM_ACK      0x8001  /* 平台通用应答 */
#define XUNJI_MSG_SERVER_CONTROL    0x7101  /* 服务器控制消息 */

/* XUNJI协议处理结果 */
#define XUNJI_OK                    0
#define XUNJI_ERROR                -1
#define XUNJI_INVALID_MSG          -2
#define XUNJI_CHECKSUM_ERROR       -3

/* XUNJI消息头结构 */
typedef struct {
    uint16_t msg_id;           /* 消息ID */
    uint16_t msg_body_attr;    /* 消息体属性 */
    uint8_t sn_bcd[6];         /* 设备序列号BCD码 */
    uint16_t msg_seq_num;      /* 消息流水号 */
} xunji_msg_header_t;

/* XUNJI消息结构 */
typedef struct {
    xunji_msg_header_t header;
    uint8_t *body;
    uint16_t body_len;
} xunji_msg_t;

/* XUNJI消息回调函数类型 */
typedef void (*xunji_message_handler_t)(const xunji_msg_t *msg, void *user_data);

/**
 * 计算XUNJI协议校验和
 * @param data 数据缓冲区
 * @param len 数据长度
 * @return 校验和
 */
uint8_t xunji_calculate_checksum(const uint8_t *data, size_t len);

/**
 * XUNJI数据转义处理
 * @param data 数据缓冲区
 * @param len 数据长度指针，处理后会更新长度
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_escape_data(uint8_t *data, size_t *len);

/**
 * XUNJI数据反转义处理
 * @param data 数据缓冲区
 * @param len 数据长度指针，处理后会更新长度
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_unescape_data(uint8_t *data, size_t *len);

/**
 * 构建XUNJI消息
 * @param msg_id 消息ID
 * @param sn_bcd 设备序列号BCD码
 * @param seq_num 消息流水号
 * @param body 消息体数据
 * @param body_len 消息体长度
 * @param output 输出缓冲区
 * @param output_size 输出缓冲区大小
 * @param actual_len 实际输出长度
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_build_message(uint16_t msg_id, const uint8_t *sn_bcd, uint16_t seq_num,
                       const uint8_t *body, uint16_t body_len,
                       uint8_t *output, size_t output_size, size_t *actual_len);

/**
 * 解析XUNJI消息
 * @param data 接收到的数据
 * @param data_len 数据长度
 * @param msg 解析后的消息结构
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_parse_message(const uint8_t *data, size_t data_len, xunji_msg_t *msg);

/**
 * 构建心跳消息
 * @param sn_bcd 设备序列号BCD码
 * @param seq_num 消息流水号
 * @param output 输出缓冲区
 * @param output_size 输出缓冲区大小
 * @param actual_len 实际输出长度
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_build_heartbeat(const uint8_t *sn_bcd, uint16_t seq_num,
                         uint8_t *output, size_t output_size, size_t *actual_len);

/**
 * 构建通用应答消息
 * @param sn_bcd 设备序列号BCD码
 * @param seq_num 消息流水号
 * @param ack_seq_num 应答的消息流水号
 * @param result 应答结果
 * @param output 输出缓冲区
 * @param output_size 输出缓冲区大小
 * @param actual_len 实际输出长度
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_build_ack(const uint8_t *sn_bcd, uint16_t seq_num, uint16_t ack_seq_num, uint8_t result,
                   uint8_t *output, size_t output_size, size_t *actual_len);

/**
 * 验证消息完整性
 * @param data 消息数据
 * @param data_len 数据长度
 * @return 1完整，0不完整
 */
int xunji_is_message_complete(const uint8_t *data, size_t data_len);

/**
 * 获取消息体长度
 * @param msg_body_attr 消息体属性
 * @return 消息体长度
 */
uint16_t xunji_get_body_length(uint16_t msg_body_attr);

/**
 * 设置消息体属性
 * @param body_len 消息体长度
 * @param encrypt 是否加密
 * @param fragment 是否分包
 * @return 消息体属性
 */
uint16_t xunji_set_body_attr(uint16_t body_len, int encrypt, int fragment);

/**
 * 字符串转BCD码
 * @param str 字符串
 * @param bcd BCD码缓冲区
 * @param bcd_size BCD码缓冲区大小
 * @return 转换的BCD码长度，失败返回-1
 */
int xunji_str_to_bcd(const char *str, uint8_t *bcd, size_t bcd_size);

/**
 * BCD码转字符串
 * @param bcd BCD码
 * @param bcd_len BCD码长度
 * @param str 字符串缓冲区
 * @param str_size 字符串缓冲区大小
 * @return XUNJI_OK成功，其他值失败
 */
int xunji_bcd_to_str(const uint8_t *bcd, size_t bcd_len, char *str, size_t str_size);

#ifdef __cplusplus
}
#endif

#endif /* XUNJI_PROTOCOL_H */
