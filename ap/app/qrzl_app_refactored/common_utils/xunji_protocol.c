/* 避免宏定义冲突，先包含系统头文件 */
#include <netinet/in.h>
#include <arpa/inet.h>

/* 避免宏重定义警告 */
#ifdef _POSIX_C_SOURCE
#undef _POSIX_C_SOURCE
#endif
#ifdef _GNU_SOURCE
#undef _GNU_SOURCE
#endif

#include "xunji_protocol.h"
#include "../inc/qrzl_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/* 计算XUNJI协议校验和 */
uint8_t xunji_calculate_checksum(const uint8_t *data, size_t len) {
    uint8_t checksum = 0;
    for (size_t i = 0; i < len; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

/* XUNJI数据转义处理 */
int xunji_escape_data(uint8_t *data, size_t *len) {
    if (!data || !len || *len == 0) {
        return XUNJI_ERROR;
    }
    
    size_t original_len = *len;
    size_t new_len = 0;
    uint8_t *temp_buffer = malloc(original_len * 2); /* 最坏情况下长度翻倍 */
    if (!temp_buffer) {
        qrzl_log_error("Failed to allocate memory for escape processing");
        return XUNJI_ERROR;
    }
    
    for (size_t i = 0; i < original_len; i++) {
        if (data[i] == XUNJI_FLAG_START_END) {
            temp_buffer[new_len++] = XUNJI_ESCAPE_FLAG;
            temp_buffer[new_len++] = XUNJI_ESCAPE_7E;
        } else if (data[i] == XUNJI_ESCAPE_FLAG) {
            temp_buffer[new_len++] = XUNJI_ESCAPE_FLAG;
            temp_buffer[new_len++] = XUNJI_ESCAPE_7D;
        } else {
            temp_buffer[new_len++] = data[i];
        }
    }
    
    if (new_len <= original_len) {
        memcpy(data, temp_buffer, new_len);
        *len = new_len;
    } else {
        qrzl_log_error("Escaped data too long for original buffer");
        free(temp_buffer);
        return XUNJI_ERROR;
    }
    
    free(temp_buffer);
    return XUNJI_OK;
}

/* XUNJI数据反转义处理 */
int xunji_unescape_data(uint8_t *data, size_t *len) {
    if (!data || !len || *len == 0) {
        return XUNJI_ERROR;
    }
    
    size_t original_len = *len;
    size_t new_len = 0;
    
    for (size_t i = 0; i < original_len; i++) {
        if (data[i] == XUNJI_ESCAPE_FLAG && i + 1 < original_len) {
            if (data[i + 1] == XUNJI_ESCAPE_7E) {
                data[new_len++] = XUNJI_FLAG_START_END;
                i++; /* 跳过下一个字节 */
            } else if (data[i + 1] == XUNJI_ESCAPE_7D) {
                data[new_len++] = XUNJI_ESCAPE_FLAG;
                i++; /* 跳过下一个字节 */
            } else {
                data[new_len++] = data[i];
            }
        } else {
            data[new_len++] = data[i];
        }
    }
    
    *len = new_len;
    return XUNJI_OK;
}

/* 构建XUNJI消息 */
int xunji_build_message(uint16_t msg_id, const uint8_t *sn_bcd, uint16_t seq_num,
                       const uint8_t *body, uint16_t body_len,
                       uint8_t *output, size_t output_size, size_t *actual_len) {
    if (!sn_bcd || !output || !actual_len) {
        return XUNJI_ERROR;
    }
    
    /* 计算所需缓冲区大小 */
    size_t header_body_len = XUNJI_HEADER_SIZE + body_len;
    size_t max_escaped_len = header_body_len * 2 + 3; /* 转义后最大长度 + 标志位 + 校验和 */
    
    if (output_size < max_escaped_len) {
        qrzl_log_error("Output buffer too small");
        return XUNJI_ERROR;
    }
    
    /* 构建消息头和消息体 */
    uint8_t *temp_buffer = malloc(header_body_len + 1); /* +1 for checksum */
    if (!temp_buffer) {
        qrzl_log_error("Failed to allocate temporary buffer");
        return XUNJI_ERROR;
    }
    
    uint8_t *ptr = temp_buffer;
    
    /* 消息ID (网络字节序) */
    uint16_t net_msg_id = htons(msg_id);
    memcpy(ptr, &net_msg_id, sizeof(net_msg_id));
    ptr += sizeof(net_msg_id);
    
    /* 消息体属性 */
    uint16_t msg_body_attr = xunji_set_body_attr(body_len, 0, 0);
    uint16_t net_msg_body_attr = htons(msg_body_attr);
    memcpy(ptr, &net_msg_body_attr, sizeof(net_msg_body_attr));
    ptr += sizeof(net_msg_body_attr);
    
    /* 设备序列号BCD码 */
    memcpy(ptr, sn_bcd, 6);
    ptr += 6;
    
    /* 消息流水号 */
    uint16_t net_seq_num = htons(seq_num);
    memcpy(ptr, &net_seq_num, sizeof(net_seq_num));
    ptr += sizeof(net_seq_num);
    
    /* 消息体 */
    if (body && body_len > 0) {
        memcpy(ptr, body, body_len);
        ptr += body_len;
    }
    
    /* 计算校验和 */
    uint8_t checksum = xunji_calculate_checksum(temp_buffer, header_body_len);
    *ptr = checksum;
    
    /* 转义处理 */
    size_t escaped_len = header_body_len + 1;
    if (xunji_escape_data(temp_buffer, &escaped_len) != XUNJI_OK) {
        free(temp_buffer);
        return XUNJI_ERROR;
    }
    
    /* 添加标志位 */
    output[0] = XUNJI_FLAG_START_END;
    memcpy(output + 1, temp_buffer, escaped_len);
    output[escaped_len + 1] = XUNJI_FLAG_START_END;
    
    *actual_len = escaped_len + 2;
    
    free(temp_buffer);
    return XUNJI_OK;
}

/* 解析XUNJI消息 */
int xunji_parse_message(const uint8_t *data, size_t data_len, xunji_msg_t *msg) {
    if (!data || data_len < 3 || !msg) {
        return XUNJI_ERROR;
    }
    
    /* 检查消息完整性 */
    if (!xunji_is_message_complete(data, data_len)) {
        return XUNJI_INVALID_MSG;
    }
    
    /* 去除标志位 */
    size_t content_len = data_len - 2;
    uint8_t *content = malloc(content_len);
    if (!content) {
        qrzl_log_error("Failed to allocate memory for message parsing");
        return XUNJI_ERROR;
    }
    
    memcpy(content, data + 1, content_len);
    
    /* 反转义处理 */
    if (xunji_unescape_data(content, &content_len) != XUNJI_OK) {
        free(content);
        return XUNJI_ERROR;
    }
    
    /* 检查最小长度 */
    if (content_len < XUNJI_HEADER_SIZE + 1) { /* 头部 + 校验和 */
        free(content);
        return XUNJI_INVALID_MSG;
    }
    
    /* 验证校验和 */
    uint8_t calculated_checksum = xunji_calculate_checksum(content, content_len - 1);
    if (calculated_checksum != content[content_len - 1]) {
        qrzl_log_error("Checksum mismatch: calculated=0x%02x, received=0x%02x", 
                      calculated_checksum, content[content_len - 1]);
        free(content);
        return XUNJI_CHECKSUM_ERROR;
    }
    
    /* 解析消息头 */
    uint8_t *ptr = content;
    
    memcpy(&msg->header.msg_id, ptr, sizeof(msg->header.msg_id));
    msg->header.msg_id = ntohs(msg->header.msg_id);
    ptr += sizeof(msg->header.msg_id);
    
    memcpy(&msg->header.msg_body_attr, ptr, sizeof(msg->header.msg_body_attr));
    msg->header.msg_body_attr = ntohs(msg->header.msg_body_attr);
    ptr += sizeof(msg->header.msg_body_attr);
    
    memcpy(msg->header.sn_bcd, ptr, sizeof(msg->header.sn_bcd));
    ptr += sizeof(msg->header.sn_bcd);
    
    memcpy(&msg->header.msg_seq_num, ptr, sizeof(msg->header.msg_seq_num));
    msg->header.msg_seq_num = ntohs(msg->header.msg_seq_num);
    ptr += sizeof(msg->header.msg_seq_num);
    
    /* 解析消息体 */
    msg->body_len = xunji_get_body_length(msg->header.msg_body_attr);
    if (msg->body_len > 0) {
        if (content_len < XUNJI_HEADER_SIZE + msg->body_len + 1) {
            free(content);
            return XUNJI_INVALID_MSG;
        }
        
        msg->body = malloc(msg->body_len);
        if (!msg->body) {
            free(content);
            return XUNJI_ERROR;
        }
        
        memcpy(msg->body, ptr, msg->body_len);
    } else {
        msg->body = NULL;
    }
    
    free(content);
    return XUNJI_OK;
}

/* 构建心跳消息 */
int xunji_build_heartbeat(const uint8_t *sn_bcd, uint16_t seq_num,
                         uint8_t *output, size_t output_size, size_t *actual_len) {
    return xunji_build_message(XUNJI_MSG_HEARTBEAT, sn_bcd, seq_num, NULL, 0, output, output_size, actual_len);
}

/* 构建通用应答消息 */
int xunji_build_ack(const uint8_t *sn_bcd, uint16_t seq_num, uint16_t ack_seq_num, uint8_t result,
                   uint8_t *output, size_t output_size, size_t *actual_len) {
    uint8_t ack_body[3];
    uint16_t net_ack_seq_num = htons(ack_seq_num);
    memcpy(ack_body, &net_ack_seq_num, sizeof(net_ack_seq_num));
    ack_body[2] = result;
    
    return xunji_build_message(XUNJI_MSG_TERMINAL_ACK, sn_bcd, seq_num, ack_body, sizeof(ack_body),
                              output, output_size, actual_len);
}

/* 验证消息完整性 */
int xunji_is_message_complete(const uint8_t *data, size_t data_len) {
    if (!data || data_len < 3) {
        return 0;
    }

    /* 检查起始和结束标志 */
    if (data[0] != XUNJI_FLAG_START_END || data[data_len - 1] != XUNJI_FLAG_START_END) {
        return 0;
    }

    return 1;
}

/* 获取消息体长度 */
uint16_t xunji_get_body_length(uint16_t msg_body_attr) {
    return msg_body_attr & 0x03FF; /* 低10位为消息体长度 */
}

/* 设置消息体属性 */
uint16_t xunji_set_body_attr(uint16_t body_len, int encrypt, int fragment) {
    uint16_t attr = body_len & 0x03FF; /* 低10位为消息体长度 */

    if (encrypt) {
        attr |= 0x0400; /* 第10位为加密标志 */
    }

    if (fragment) {
        attr |= 0x2000; /* 第13位为分包标志 */
    }

    return attr;
}

/* 字符串转BCD码 */
int xunji_str_to_bcd(const char *str, uint8_t *bcd, size_t bcd_size) {
    if (!str || !bcd || bcd_size == 0) {
        return -1;
    }

    size_t str_len = strlen(str);
    size_t bcd_len = (str_len + 1) / 2;

    if (bcd_len > bcd_size) {
        return -1;
    }

    memset(bcd, 0, bcd_size);

    for (size_t i = 0; i < str_len; i++) {
        char c = str[i];
        if (c < '0' || c > '9') {
            return -1; /* 非数字字符 */
        }

        uint8_t digit = c - '0';
        size_t bcd_index = i / 2;

        if (i % 2 == 0) {
            bcd[bcd_index] = (digit << 4);
        } else {
            bcd[bcd_index] |= digit;
        }
    }

    /* 如果字符串长度为奇数，最后一个BCD字节的低4位补0 */
    if (str_len % 2 == 1) {
        /* 已经在上面处理了 */
    }

    return bcd_len;
}

/* BCD码转字符串 */
int xunji_bcd_to_str(const uint8_t *bcd, size_t bcd_len, char *str, size_t str_size) {
    if (!bcd || !str || str_size == 0) {
        return XUNJI_ERROR;
    }

    size_t max_str_len = bcd_len * 2;
    if (str_size <= max_str_len) {
        return XUNJI_ERROR;
    }

    for (size_t i = 0; i < bcd_len; i++) {
        uint8_t high_digit = (bcd[i] >> 4) & 0x0F;
        uint8_t low_digit = bcd[i] & 0x0F;

        if (high_digit > 9 || low_digit > 9) {
            return XUNJI_ERROR; /* 无效的BCD码 */
        }

        str[i * 2] = '0' + high_digit;
        str[i * 2 + 1] = '0' + low_digit;
    }

    str[max_str_len] = '\0';
    return XUNJI_OK;
}
