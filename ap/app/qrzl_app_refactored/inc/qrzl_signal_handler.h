#ifndef __QRZL_SIGNAL_HANDLER_H__
#define __QRZL_SIGNAL_HANDLER_H__

#include "qrzl_common.h"

/* 信号处理回调函数类型 */
typedef void (*qrzl_signal_callback_t)(int signum, void *user_data);

/* 信号处理器结构体 */
typedef struct qrzl_signal_handler {
    int signum;
    qrzl_signal_callback_t callback;
    void *user_data;
    struct qrzl_signal_handler *next;
} qrzl_signal_handler_t;

/* 信号管理器结构体 */
typedef struct {
    qrzl_signal_handler_t *handler_list;
    pthread_mutex_t mutex;
    bool initialized;
    sigset_t signal_mask;
    pthread_t signal_thread;
} qrzl_signal_manager_t;

/**
 * 初始化信号处理系统
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_init(void);

/**
 * 销毁信号处理系统
 */
void qrzl_signal_destroy(void);

/**
 * 注册信号处理器
 * @param signum 信号编号
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_register(int signum, qrzl_signal_callback_t callback, void *user_data);

/**
 * 注销信号处理器
 * @param signum 信号编号
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_unregister(int signum);

/**
 * 阻塞等待信号
 * @param timeout_ms 超时时间(毫秒)，0表示无限等待
 * @return 接收到的信号编号，-1表示超时或错误
 */
int qrzl_signal_wait(uint32_t timeout_ms);

/**
 * 发送信号给自己
 * @param signum 信号编号
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_send_self(int signum);

/**
 * 屏蔽信号
 * @param signum 信号编号
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_block(int signum);

/**
 * 解除信号屏蔽
 * @param signum 信号编号
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_unblock(int signum);

/**
 * 检查是否收到退出信号
 * @return true 收到退出信号，false 未收到
 */
bool qrzl_signal_is_shutdown_requested(void);

/**
 * 设置退出标志
 */
void qrzl_signal_set_shutdown_flag(void);

/**
 * 清除退出标志
 */
void qrzl_signal_clear_shutdown_flag(void);

/**
 * 等待退出信号
 * @param timeout_ms 超时时间(毫秒)，0表示无限等待
 * @return true 收到退出信号，false 超时
 */
bool qrzl_signal_wait_for_shutdown(uint32_t timeout_ms);

/* 默认信号处理函数 */
void qrzl_signal_default_sigint_handler(int signum, void *user_data);
void qrzl_signal_default_sigterm_handler(int signum, void *user_data);
void qrzl_signal_default_sigpipe_handler(int signum, void *user_data);
void qrzl_signal_default_sigchld_handler(int signum, void *user_data);

/**
 * 注册默认信号处理器
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_signal_register_defaults(void);

/**
 * 优雅退出处理
 */
void qrzl_signal_graceful_shutdown(void);

/* 信号处理宏 */
#define QRZL_SIGNAL_HANDLER_DECLARE(name) \
    void qrzl_signal_##name##_handler(int signum, void *user_data)

#define QRZL_SIGNAL_HANDLER_BEGIN(name) \
    void qrzl_signal_##name##_handler(int signum, void *user_data) { \
        qrzl_log_info("Signal %s (%d) received", #name, signum);

#define QRZL_SIGNAL_HANDLER_END(name) \
        qrzl_log_info("Signal %s (%d) handled", #name, signum); \
    }

/* 常用信号定义 */
#ifndef SIGINT
#define SIGINT  2   /* Interrupt (ANSI) */
#endif

#ifndef SIGTERM
#define SIGTERM 15  /* Termination (ANSI) */
#endif

#ifndef SIGPIPE
#define SIGPIPE 13  /* Broken pipe (POSIX) */
#endif

#ifndef SIGCHLD
#define SIGCHLD 17  /* Child status changed (POSIX) */
#endif

#ifndef SIGUSR1
#define SIGUSR1 10  /* User-defined signal 1 (POSIX) */
#endif

#ifndef SIGUSR2
#define SIGUSR2 12  /* User-defined signal 2 (POSIX) */
#endif

/* 信号名称字符串 */
const char* qrzl_signal_get_name(int signum);

#endif /* __QRZL_SIGNAL_HANDLER_H__ */
