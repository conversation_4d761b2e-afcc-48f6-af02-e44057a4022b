#ifndef __QRZL_NETWORK_CLIENT_H__
#define __QRZL_NETWORK_CLIENT_H__

#include "qrzl_common.h"

/* 网络协议类型 */
typedef enum {
    QRZL_PROTOCOL_HTTP = 0,
    QRZL_PROTOCOL_HTTPS,
    QRZL_PROTOCOL_MQTT,
    QRZL_PROTOCOL_MQTTS,
    QRZL_PROTOCOL_TCP,
    QRZL_PROTOCOL_UDP
} qrzl_protocol_type_t;

/* 请求方法类型 */
typedef enum {
    QRZL_METHOD_GET = 0,
    QRZL_METHOD_POST,
    QRZL_METHOD_PUT,
    QRZL_METHOD_DELETE,
    QRZL_METHOD_HEAD,
    QRZL_METHOD_OPTIONS
} qrzl_http_method_t;

/* 连接状态 */
typedef enum {
    QRZL_CONN_STATE_DISCONNECTED = 0,
    QRZL_CONN_STATE_CONNECTING,
    QRZL_CONN_STATE_CONNECTED,
    QRZL_CONN_STATE_ERROR
} qrzl_connection_state_t;

/* 客户端类型 */
typedef enum {
    QRZL_CLIENT_HTTP = 0,
    QRZL_CLIENT_HTTPS,
    QRZL_CLIENT_MQTT,
    QRZL_CLIENT_TCP,
    QRZL_CLIENT_MAX
} qrzl_client_type_t;

/* 客户端状态 */
typedef enum {
    QRZL_CLIENT_STATE_IDLE = 0,
    QRZL_CLIENT_STATE_CONNECTING,
    QRZL_CLIENT_STATE_CONNECTED,
    QRZL_CLIENT_STATE_DISCONNECTED,
    QRZL_CLIENT_STATE_ERROR,
    QRZL_CLIENT_STATE_MAX
} qrzl_client_state_t;

/* MQTT QoS级别 */
typedef enum {
    QRZL_MQTT_QOS_0 = 0,
    QRZL_MQTT_QOS_1 = 1,
    QRZL_MQTT_QOS_2 = 2
} qrzl_mqtt_qos_t;

/* 网络客户端ID类型 */
typedef uint32_t qrzl_client_id_t;
#define QRZL_INVALID_CLIENT_ID 0

/* 简化的客户端配置结构体 */
typedef struct {
    qrzl_client_type_t type;
    char client_id[64];
    uint32_t timeout_ms;
} qrzl_simple_client_config_t;

/* 客户端回调函数类型 */
typedef void (*qrzl_client_callback_t)(qrzl_client_id_t client_id, int event, void *data);

/* 客户端回调结构体 */
typedef struct {
    qrzl_client_callback_t on_connect;
    qrzl_client_callback_t on_disconnect;
    qrzl_client_callback_t on_message;
    qrzl_client_callback_t on_error;
} qrzl_client_callbacks_t;

/* MQTT连接参数结构体 */
typedef struct {
    const char *host;
    uint16_t port;
    const char *client_id;
    const char *username;
    const char *password;
    uint16_t keepalive;
    bool clean_session;
    bool use_ssl;
} qrzl_mqtt_connect_params_t;

/* HTTP请求结构体 */
typedef struct {
    qrzl_http_method_t method;
    char url[QRZL_MAX_PATH_LEN];
    char headers[QRZL_MAX_STRING_LEN];
    char body[QRZL_MAX_BUFFER_LEN];
    uint32_t timeout_ms;
} qrzl_http_request_t;

/* HTTP响应结构体 */
typedef struct {
    int status_code;
    char headers[QRZL_MAX_STRING_LEN];
    char body[QRZL_MAX_RESPONSE_LEN];
    size_t body_len;
} qrzl_http_response_t;

/* MQTT配置结构体 */
typedef struct {
    char broker_host[128];
    uint16_t broker_port;
    char client_id[64];
    char username[64];
    char password[64];
    uint16_t keep_alive;
    bool clean_session;
    uint8_t qos;
} qrzl_mqtt_config_t;

/* MQTT消息结构体 */
typedef struct {
    char topic[128];
    char payload[QRZL_MAX_BUFFER_LEN];
    size_t payload_len;
    uint8_t qos;
    bool retained;
} qrzl_mqtt_message_t;

/* 网络回调函数类型 */
typedef void (*qrzl_http_response_callback_t)(qrzl_client_id_t client_id, 
                                              const qrzl_http_response_t *response, 
                                              void *user_data);

typedef void (*qrzl_mqtt_message_callback_t)(qrzl_client_id_t client_id,
                                             const qrzl_mqtt_message_t *message,
                                             void *user_data);

typedef void (*qrzl_connection_callback_t)(qrzl_client_id_t client_id,
                                           qrzl_connection_state_t state,
                                           void *user_data);

/* 网络客户端配置结构体 */
typedef struct {
    qrzl_protocol_type_t protocol;
    union {
        struct {
            uint32_t timeout_ms;
            uint32_t retry_count;
            uint32_t retry_interval_ms;
        } http;
        qrzl_mqtt_config_t mqtt;
        struct {
            char host[128];
            uint16_t port;
            uint32_t timeout_ms;
        } tcp;
    } config;
    qrzl_http_response_callback_t http_callback;
    qrzl_mqtt_message_callback_t mqtt_callback;
    qrzl_connection_callback_t conn_callback;
    void *user_data;
} qrzl_client_config_t;

/**
 * 初始化网络客户端系统
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_network_init(void);

/**
 * 销毁网络客户端系统
 */
void qrzl_network_destroy(void);

/**
 * 创建网络客户端
 * @param config 客户端配置
 * @return 客户端ID，QRZL_INVALID_CLIENT_ID表示失败
 */
qrzl_client_id_t qrzl_client_create(const qrzl_simple_client_config_t *config);

/**
 * 销毁网络客户端
 * @param client_id 客户端ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_client_destroy(qrzl_client_id_t client_id);

/**
 * 连接服务器
 * @param client_id 客户端ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_client_connect(qrzl_client_id_t client_id);

/**
 * 断开连接
 * @param client_id 客户端ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_client_disconnect(qrzl_client_id_t client_id);

/**
 * 获取连接状态
 * @param client_id 客户端ID
 * @return 连接状态
 */
qrzl_connection_state_t qrzl_client_get_state(qrzl_client_id_t client_id);

/**
 * 发送HTTP请求
 * @param client_id 客户端ID
 * @param request 请求结构体
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_http_send_request(qrzl_client_id_t client_id, const qrzl_http_request_t *request);

/**
 * 发送HTTP GET请求
 * @param client_id 客户端ID
 * @param url URL地址
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @param timeout_ms 超时时间(毫秒)
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_http_get(qrzl_client_id_t client_id, const char *url,
                  char *response, size_t resp_size, uint32_t timeout_ms);

/**
 * 发送HTTP POST请求
 * @param client_id 客户端ID
 * @param url URL地址
 * @param body 请求体
 * @param response 响应缓冲区
 * @param resp_size 响应缓冲区大小
 * @param timeout_ms 超时时间(毫秒)
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_http_post(qrzl_client_id_t client_id, const char *url, const char *body,
                   char *response, size_t resp_size, uint32_t timeout_ms);

/**
 * 发布MQTT消息
 * @param client_id 客户端ID
 * @param message 消息结构体
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_mqtt_publish(qrzl_client_id_t client_id, const qrzl_mqtt_message_t *message);

/**
 * 订阅MQTT主题
 * @param client_id 客户端ID
 * @param topic 主题
 * @param qos QoS等级
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_mqtt_subscribe(qrzl_client_id_t client_id, const char *topic, qrzl_mqtt_qos_t qos);

/**
 * 取消订阅MQTT主题
 * @param client_id 客户端ID
 * @param topic 主题
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_mqtt_unsubscribe(qrzl_client_id_t client_id, const char *topic);

/**
 * 检查网络连通性
 * @param host 主机地址
 * @param timeout_ms 超时时间(毫秒)
 * @return true 连通，false 不连通
 */
bool qrzl_network_check_connectivity(const char *host, uint32_t timeout_ms);

/**
 * 获取网络统计信息
 * @param client_id 客户端ID
 * @param tx_bytes 发送字节数(输出参数)
 * @param rx_bytes 接收字节数(输出参数)
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_client_get_stats(qrzl_client_id_t client_id, uint64_t *tx_bytes, uint64_t *rx_bytes);

/* 便利宏定义 */
#define QRZL_HTTP_TIMEOUT_DEFAULT   10000   /* 10秒 */
#define QRZL_HTTP_RETRY_COUNT_DEFAULT   3
#define QRZL_HTTP_RETRY_INTERVAL_DEFAULT 5000   /* 5秒 */

#define QRZL_MQTT_KEEP_ALIVE_DEFAULT    60
#define QRZL_MQTT_QOS_DEFAULT           1

/* HTTP状态码定义 */
#define QRZL_HTTP_STATUS_OK             200
#define QRZL_HTTP_STATUS_CREATED        201
#define QRZL_HTTP_STATUS_BAD_REQUEST    400
#define QRZL_HTTP_STATUS_UNAUTHORIZED   401
#define QRZL_HTTP_STATUS_NOT_FOUND      404
#define QRZL_HTTP_STATUS_SERVER_ERROR   500

#endif /* __QRZL_NETWORK_CLIENT_H__ */
