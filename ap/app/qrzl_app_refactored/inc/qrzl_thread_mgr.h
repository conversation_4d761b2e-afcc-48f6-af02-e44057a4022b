#ifndef __QRZL_THREAD_MGR_H__
#define __QRZL_THREAD_MGR_H__

#include "qrzl_common.h"

/* 线程状态 */
typedef enum {
    QRZL_THREAD_STATE_IDLE = 0,
    QRZL_THREAD_STATE_RUNNING,
    QRZL_THREAD_STATE_STOPPING,
    QRZL_THREAD_STATE_STOPPED,
    QRZL_THREAD_STATE_ERROR
} qrzl_thread_state_t;

/* 线程类型 */
typedef enum {
    QRZL_THREAD_TYPE_MAIN = 0,
    QRZL_THREAD_TYPE_CLOUD_CLIENT,
    QRZL_THREAD_TYPE_DEVICE_MONITOR,
    QRZL_THREAD_TYPE_NETWORK_MONITOR,
    QRZL_THREAD_TYPE_AUTH_HANDLER,
    QRZL_THREAD_TYPE_MESSAGE_HANDLER,
    QRZL_THREAD_TYPE_TIMER,
    QRZL_THREAD_TYPE_WORKER
} qrzl_thread_type_t;

/* 线程ID类型 */
typedef uint32_t qrzl_thread_id_t;
#define QRZL_INVALID_THREAD_ID 0

/* 线程函数类型 */
typedef void* (*qrzl_thread_func_t)(void *arg);

/* 线程信息结构体 */
typedef struct qrzl_thread_info {
    qrzl_thread_id_t id;
    pthread_t pthread_id;
    qrzl_thread_type_t type;
    qrzl_thread_state_t state;
    char name[32];
    qrzl_thread_func_t func;
    void *arg;
    time_t create_time;
    time_t start_time;
    volatile bool should_stop;
    struct qrzl_thread_info *next;
} qrzl_thread_info_t;

/* 线程管理器结构体 */
typedef struct {
    qrzl_thread_info_t *thread_list;
    pthread_mutex_t mutex;
    uint32_t next_thread_id;
    uint32_t thread_count;
    bool initialized;
} qrzl_thread_manager_t;

/**
 * 初始化线程管理器
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_thread_mgr_init(void);

/**
 * 销毁线程管理器
 */
void qrzl_thread_mgr_destroy(void);

/**
 * 创建线程
 * @param type 线程类型
 * @param name 线程名称
 * @param func 线程函数
 * @param arg 线程参数
 * @return 线程ID，QRZL_INVALID_THREAD_ID表示失败
 */
qrzl_thread_id_t qrzl_thread_create(qrzl_thread_type_t type,
                                    const char *name,
                                    qrzl_thread_func_t func,
                                    void *arg);

/**
 * 启动线程
 * @param thread_id 线程ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_thread_start(qrzl_thread_id_t thread_id);

/**
 * 停止线程
 * @param thread_id 线程ID
 * @param timeout_ms 超时时间(毫秒)，0表示立即返回
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_thread_stop(qrzl_thread_id_t thread_id, uint32_t timeout_ms);

/**
 * 等待线程结束
 * @param thread_id 线程ID
 * @param timeout_ms 超时时间(毫秒)，0表示无限等待
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_thread_join(qrzl_thread_id_t thread_id, uint32_t timeout_ms);

/**
 * 获取线程状态
 * @param thread_id 线程ID
 * @return 线程状态
 */
qrzl_thread_state_t qrzl_thread_get_state(qrzl_thread_id_t thread_id);

/**
 * 检查线程是否应该停止
 * @param thread_id 线程ID
 * @return true 应该停止，false 继续运行
 */
bool qrzl_thread_should_stop(qrzl_thread_id_t thread_id);

/**
 * 设置线程停止标志
 * @param thread_id 线程ID
 */
void qrzl_thread_set_stop_flag(qrzl_thread_id_t thread_id);

/**
 * 获取当前线程ID
 * @return 当前线程ID，QRZL_INVALID_THREAD_ID表示未找到
 */
qrzl_thread_id_t qrzl_thread_get_current_id(void);

/**
 * 停止所有线程
 * @param timeout_ms 超时时间(毫秒)
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_thread_stop_all(uint32_t timeout_ms);

/**
 * 获取线程数量
 * @return 线程数量
 */
uint32_t qrzl_thread_get_count(void);

/**
 * 打印线程信息
 */
void qrzl_thread_print_info(void);

/**
 * 线程安全的睡眠函数
 * @param thread_id 线程ID
 * @param ms 睡眠时间(毫秒)
 * @return true 正常睡眠结束，false 被中断
 */
bool qrzl_thread_sleep_ms(qrzl_thread_id_t thread_id, uint32_t ms);

/**
 * 线程安全的秒级睡眠函数
 * @param thread_id 线程ID
 * @param seconds 睡眠时间(秒)
 * @return true 正常睡眠结束，false 被中断
 */
bool qrzl_thread_sleep(qrzl_thread_id_t thread_id, uint32_t seconds);

/* 线程函数包装宏 */
#define QRZL_THREAD_FUNC_DECLARE(name) \
    void* qrzl_thread_##name(void *arg)

#define QRZL_THREAD_FUNC_BEGIN(name) \
    void* qrzl_thread_##name(void *arg) { \
        (void)arg; /* 避免未使用参数警告 */ \
        qrzl_thread_id_t thread_id = qrzl_thread_get_current_id(); \
        qrzl_log_info("Thread %s started", #name);

#define QRZL_THREAD_FUNC_END(name) \
        qrzl_log_info("Thread %s ended", #name); \
        return NULL; \
    }

#define QRZL_THREAD_CHECK_STOP(thread_id) \
    if (qrzl_thread_should_stop(thread_id)) { \
        qrzl_log_info("Thread received stop signal"); \
        break; \
    }

#define QRZL_THREAD_SLEEP_CHECK(thread_id, ms) \
    if (!qrzl_thread_sleep_ms(thread_id, ms)) { \
        qrzl_log_info("Thread sleep interrupted"); \
        break; \
    }

/* 预定义的线程名称 */
#define QRZL_THREAD_NAME_MAIN           "qrzl_main"
#define QRZL_THREAD_NAME_TIMER          "qrzl_timer"
#define QRZL_THREAD_NAME_CLOUD_HTTP     "qrzl_cloud_http"
#define QRZL_THREAD_NAME_CLOUD_MQTT     "qrzl_cloud_mqtt"
#define QRZL_THREAD_NAME_DEVICE_CTRL    "qrzl_device_ctrl"
#define QRZL_THREAD_NAME_NET_MONITOR    "qrzl_net_monitor"
#define QRZL_THREAD_NAME_SIM_MONITOR    "qrzl_sim_monitor"
#define QRZL_THREAD_NAME_AUTH_HANDLER   "qrzl_auth_handler"
#define QRZL_THREAD_NAME_MSG_HANDLER    "qrzl_msg_handler"

#endif /* __QRZL_THREAD_MGR_H__ */
