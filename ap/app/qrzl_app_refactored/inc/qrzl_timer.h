#ifndef __QRZL_TIMER_H__
#define __QRZL_TIMER_H__

#include "qrzl_common.h"

#include "soft_timer.h"

/* 定时器类型 */
typedef enum {
    QRZL_TIMER_ONESHOT = 0,    /* 一次性定时器 */
    QRZL_TIMER_PERIODIC        /* 周期性定时器 */
} qrzl_timer_type_t;

/* 定时器状态 */
typedef enum {
    QRZL_TIMER_STATE_IDLE = 0,
    QRZL_TIMER_STATE_RUNNING,
    QRZL_TIMER_STATE_EXPIRED,
    QRZL_TIMER_STATE_CANCELLED
} qrzl_timer_state_t;

/* 定时器ID类型 */
typedef uint32_t qrzl_timer_id_t;
#define QRZL_INVALID_TIMER_ID 0

/* 定时器回调函数类型 */
typedef void (*qrzl_timer_callback_t)(qrzl_timer_id_t timer_id, void *user_data);

/* 定时器结构体 */
typedef struct qrzl_timer {
    qrzl_timer_id_t id;
    qrzl_timer_type_t type;
    qrzl_timer_state_t state;
    uint32_t interval_ms;      /* 定时器间隔(毫秒) */
    uint32_t remaining_ms;     /* 剩余时间(毫秒) */
    qrzl_timer_callback_t callback;
    void *user_data;
    struct qrzl_timer *next;
} qrzl_timer_t;

/* 定时器管理器结构体 */
typedef struct {
    qrzl_timer_t *timer_list;
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    pthread_t thread_id;
    bool running;
    uint32_t next_timer_id;
} qrzl_timer_manager_t;

/* 预定义的定时器ID */
#define QRZL_TIMER_ID_CLOUD_REQUEST     1001
#define QRZL_TIMER_ID_DEVICE_MONITOR    1002
#define QRZL_TIMER_ID_NETWORK_CHECK     1003
#define QRZL_TIMER_ID_SIM_SWITCH        1004
#define QRZL_TIMER_ID_BATTERY_CHECK     1005
#define QRZL_TIMER_ID_HEARTBEAT         1006

/**
 * 初始化定时器系统
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_timer_init(void);

/**
 * 销毁定时器系统
 */
void qrzl_timer_destroy(void);

/**
 * 创建定时器
 * @param type 定时器类型
 * @param interval_ms 定时器间隔(毫秒)
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 定时器ID，QRZL_INVALID_TIMER_ID表示失败
 */
qrzl_timer_id_t qrzl_timer_create(qrzl_timer_type_t type, 
                                  uint32_t interval_ms,
                                  qrzl_timer_callback_t callback,
                                  void *user_data);

/**
 * 启动定时器
 * @param timer_id 定时器ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_timer_start(qrzl_timer_id_t timer_id);

/**
 * 停止定时器
 * @param timer_id 定时器ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_timer_stop(qrzl_timer_id_t timer_id);

/**
 * 删除定时器
 * @param timer_id 定时器ID
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_timer_delete(qrzl_timer_id_t timer_id);

/**
 * 重置定时器
 * @param timer_id 定时器ID
 * @param interval_ms 新的间隔时间(毫秒)
 * @return QRZL_SUCCESS 成功，其他值失败
 */
int qrzl_timer_reset(qrzl_timer_id_t timer_id, uint32_t interval_ms);

/**
 * 获取定时器状态
 * @param timer_id 定时器ID
 * @return 定时器状态
 */
qrzl_timer_state_t qrzl_timer_get_state(qrzl_timer_id_t timer_id);

/**
 * 获取定时器剩余时间
 * @param timer_id 定时器ID
 * @return 剩余时间(毫秒)，0表示已过期或不存在
 */
uint32_t qrzl_timer_get_remaining(qrzl_timer_id_t timer_id);

/**
 * 延迟执行函数(替代sleep)
 * @param ms 延迟时间(毫秒)
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 定时器ID，QRZL_INVALID_TIMER_ID表示失败
 */
qrzl_timer_id_t qrzl_timer_delay(uint32_t ms, 
                                 qrzl_timer_callback_t callback,
                                 void *user_data);

/**
 * 周期性执行函数
 * @param interval_ms 间隔时间(毫秒)
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 定时器ID，QRZL_INVALID_TIMER_ID表示失败
 */
qrzl_timer_id_t qrzl_timer_periodic(uint32_t interval_ms,
                                    qrzl_timer_callback_t callback,
                                    void *user_data);

/**
 * 获取当前时间戳(毫秒)
 * @return 时间戳
 */
uint64_t qrzl_timer_get_timestamp_ms(void);

/**
 * 毫秒级睡眠(非阻塞式，基于定时器实现)
 * @param ms 睡眠时间(毫秒)
 */
void qrzl_timer_sleep_ms(uint32_t ms);

/**
 * 秒级睡眠(非阻塞式，基于定时器实现)
 * @param seconds 睡眠时间(秒)
 */
void qrzl_timer_sleep(uint32_t seconds);

/* 便利宏定义 */
#define QRZL_TIMER_SEC_TO_MS(s) ((s) * 1000)
#define QRZL_TIMER_MIN_TO_MS(m) ((m) * 60 * 1000)
#define QRZL_TIMER_HOUR_TO_MS(h) ((h) * 60 * 60 * 1000)

/* 常用定时器间隔定义 */
#define QRZL_TIMER_INTERVAL_1S      1000
#define QRZL_TIMER_INTERVAL_5S      5000
#define QRZL_TIMER_INTERVAL_10S     10000
#define QRZL_TIMER_INTERVAL_30S     30000
#define QRZL_TIMER_INTERVAL_1MIN    60000
#define QRZL_TIMER_INTERVAL_5MIN    300000
#define QRZL_TIMER_INTERVAL_10MIN   600000
#define QRZL_TIMER_INTERVAL_30MIN   1800000
#define QRZL_TIMER_INTERVAL_1HOUR   3600000

#endif /* __QRZL_TIMER_H__ */
