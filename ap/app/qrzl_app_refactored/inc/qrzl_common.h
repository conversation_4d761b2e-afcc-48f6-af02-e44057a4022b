#ifndef __QRZL_COMMON_H__
#define __QRZL_COMMON_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <signal.h>
#include <errno.h>
#include <time.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <stdint.h>
#include <stdbool.h>
#include <assert.h>
#include <stdarg.h>
#include <sys/prctl.h>

/* 公共宏定义 */
#define QRZL_SUCCESS                0
#define QRZL_ERROR                 -1
#define QRZL_TIMEOUT               -2
#define QRZL_INVALID_PARAM         -3
#define QRZL_NO_MEMORY             -4

#define QRZL_MAX_PATH_LEN          256
#define QRZL_MAX_STRING_LEN        512
#define QRZL_MAX_BUFFER_LEN        4096
#define QRZL_MAX_RESPONSE_LEN      5120

/* 日志级别 */
typedef enum {
    QRZL_LOG_ERROR = 0,
    QRZL_LOG_WARN  = 1,
    QRZL_LOG_INFO  = 2,
    QRZL_LOG_DEBUG = 3
} qrzl_log_level_t;

/* 应用状态 */
typedef enum {
    QRZL_APP_STATE_INIT = 0,
    QRZL_APP_STATE_RUNNING,
    QRZL_APP_STATE_STOPPING,
    QRZL_APP_STATE_STOPPED
} qrzl_app_state_t;

/* 全局应用状态 */
extern volatile qrzl_app_state_t g_qrzl_app_state;
extern volatile sig_atomic_t g_qrzl_shutdown_flag;

/* 日志宏 */
#define qrzl_log_error(fmt, ...) \
    qrzl_log_print(QRZL_LOG_ERROR, __FILE__, __LINE__, fmt, ##__VA_ARGS__)

#define qrzl_log_warn(fmt, ...) \
    qrzl_log_print(QRZL_LOG_WARN, __FILE__, __LINE__, fmt, ##__VA_ARGS__)

#define qrzl_log_info(fmt, ...) \
    qrzl_log_print(QRZL_LOG_INFO, __FILE__, __LINE__, fmt, ##__VA_ARGS__)

#define qrzl_log_debug(fmt, ...) \
    qrzl_log_print(QRZL_LOG_DEBUG, __FILE__, __LINE__, fmt, ##__VA_ARGS__)

/* 兼容旧的日志宏 */
#define qrzl_log(fmt, ...) qrzl_log_info(fmt, ##__VA_ARGS__)
#define qrzl_err(fmt, ...) qrzl_log_error(fmt, ##__VA_ARGS__)

/* 设备静态数据结构 */
struct qrzl_device_static_data {
    char sn[32];
    char imei[32];
    char mac[32];
    char model[32];
    char version[32];
    char nvro_esim1_iccid[21];
    char nvro_esim2_iccid[21];
    char nvro_esim3_iccid[21];
    char nvro_esim1_imsi[16];
    char nvro_esim2_imsi[16];
    char nvro_esim3_imsi[16];
};

/* 设备动态数据结构 */
struct qrzl_device_dynamic_data {
    uint8_t battery_level;
    uint8_t signal_strength;
    uint8_t network_type;
    uint8_t current_sim_index;
    uint8_t esim1_net_status;
    uint8_t esim2_net_status;
    uint8_t esim3_net_status;
    uint8_t is_charging;
    uint8_t is_test_net;
    uint64_t total_rx_bytes;
    uint64_t total_tx_bytes;
    uint32_t connected_devices;
    time_t last_update_time;
};

/* 全局设备数据 */
extern struct qrzl_device_static_data g_qrzl_device_static_data;
extern struct qrzl_device_dynamic_data g_qrzl_device_dynamic_data;

/* 消息队列相关 - 使用系统定义 */
/* MODULE_ID_QRZL_APP 由系统头文件定义，不需要重新定义 */

typedef struct {
    long mtype;
    uint16_t usMsgCmd;
    uint16_t usDataLen;
    uint8_t aucDataBuf[256];
} qrzl_msg_buf_t;

// /* 消息命令定义 - 避免与系统定义冲突 */
// #define MSG_CMD_QRZL_APP_SET_BAND        0x2001
// #define MSG_CMD_QRZL_APP_WIFI_CONNECTED  0x1002
// #define MSG_CMD_QRZL_APP_WIFI_DISCONNECT 0x1003

/* 函数声明 */
void qrzl_log_print(qrzl_log_level_t level, const char *file, int line, 
                   const char *fmt, ...);
void qrzl_set_process_name(const char *name);
void qrzl_set_thread_name(const char *name);

/* 内存管理宏 */
#define QRZL_MALLOC(size) malloc(size)
#define QRZL_FREE(ptr) do { if(ptr) { free(ptr); ptr = NULL; } } while(0)
#define QRZL_CALLOC(count, size) calloc(count, size)
#define QRZL_REALLOC(ptr, size) realloc(ptr, size)

/* 字符串安全操作宏 */
#define QRZL_STRNCPY(dst, src, size) do { \
    strncpy(dst, src, size - 1); \
    dst[size - 1] = '\0'; \
} while(0)

#define QRZL_SNPRINTF(dst, size, fmt, ...) \
    snprintf(dst, size, fmt, ##__VA_ARGS__)

/* 时间相关宏 */
#define QRZL_MSEC_TO_USEC(ms) ((ms) * 1000)
#define QRZL_SEC_TO_MSEC(s) ((s) * 1000)
#define QRZL_SEC_TO_USEC(s) ((s) * 1000000)

/* 数组大小宏 */
#define QRZL_ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))

/* 最小最大值宏 */
#define QRZL_MIN(a, b) ((a) < (b) ? (a) : (b))
#define QRZL_MAX(a, b) ((a) > (b) ? (a) : (b))

/* 位操作宏 */
#define QRZL_SET_BIT(val, bit) ((val) |= (1 << (bit)))
#define QRZL_CLEAR_BIT(val, bit) ((val) &= ~(1 << (bit)))
#define QRZL_TEST_BIT(val, bit) ((val) & (1 << (bit)))

/* 配置项名称定义 */
#define NV_QRZL_CLOUD_PROTOCOL_TYPE      "qrzl_cloud_protocol_type"
#define NV_QRZL_CLOUD_HTTP_PATH          "qrzl_cloud_http_path"
#define NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME "qrzl_cloud_request_interval_time"

#endif /* __QRZL_COMMON_H__ */
