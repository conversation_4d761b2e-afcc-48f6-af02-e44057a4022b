#ifndef MY_HTTP_CLIENT_H
#define MY_HTTP_CLIENT_H

#include "../qrzl_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef QRZL_HTTP_CLIENT_XUNJI  /* MY客户使用XUNJI类型的HTTP客户端 */

/**
 * 初始化MY HTTP客户端
 * @return QRZL_SUCCESS成功，其他值失败
 */
int my_http_client_init(void);

/**
 * 销毁MY HTTP客户端
 */
void my_http_client_destroy(void);

/**
 * 上报设备信息
 * @return QRZL_SUCCESS成功，其他值失败
 */
int my_http_report_device_info(void);

/**
 * 上报设备状态
 * @return QRZL_SUCCESS成功，其他值失败
 */
int my_http_report_device_status(void);

/**
 * 获取设备配置
 * @return QRZL_SUCCESS成功，其他值失败
 */
int my_http_get_device_config(void);

/**
 * 处理云端控制命令
 * @param command 命令字符串
 * @return QRZL_SUCCESS成功，其他值失败
 */
int my_http_handle_command(const char *command);

/**
 * 上报设备事件
 * @param event_type 事件类型
 * @param event_data 事件数据
 * @return QRZL_SUCCESS成功，其他值失败
 */
int my_http_report_event(const char *event_type, const char *event_data);

/**
 * XUNJI云端客户端启动函数 - 兼容原有接口
 * 支持TCP+HTTP协议，实现xunji_cloud_client_start功能
 */
void xunji_cloud_client_start(void);

#else
/* 当不是MY客户时的空实现 */
static inline int my_http_client_init(void) { return QRZL_SUCCESS; }
static inline void my_http_client_destroy(void) {}
static inline int my_http_report_device_info(void) { return QRZL_SUCCESS; }
static inline int my_http_report_device_status(void) { return QRZL_SUCCESS; }
static inline int my_http_get_device_config(void) { return QRZL_SUCCESS; }
static inline int my_http_handle_command(const char *command) { return QRZL_SUCCESS; }
static inline int my_http_report_event(const char *event_type, const char *event_data) { return QRZL_SUCCESS; }
#endif

#ifdef __cplusplus
}
#endif

#endif /* MY_HTTP_CLIENT_H */
