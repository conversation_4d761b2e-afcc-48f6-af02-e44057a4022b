#include "../inc/qrzl_signal_handler.h"
#include "../inc/qrzl_thread_mgr.h"
#include "../inc/qrzl_timer.h"

/* 全局信号管理器 */
static qrzl_signal_manager_t g_signal_manager = {0};

/* 信号线程函数 */
static void* qrzl_signal_thread_func(void *arg);

/* 初始化信号处理系统 */
int qrzl_signal_init(void) {
    if (g_signal_manager.initialized) {
        return QRZL_SUCCESS;
    }

    memset(&g_signal_manager, 0, sizeof(g_signal_manager));
    
    if (pthread_mutex_init(&g_signal_manager.mutex, NULL) != 0) {
        qrzl_log_error("Failed to init signal mutex: %s", strerror(errno));
        return QRZL_ERROR;
    }

    /* 初始化信号掩码 */
    sigemptyset(&g_signal_manager.signal_mask);
    sigaddset(&g_signal_manager.signal_mask, SIGINT);
    sigaddset(&g_signal_manager.signal_mask, SIGTERM);
    sigaddset(&g_signal_manager.signal_mask, SIGPIPE);
    sigaddset(&g_signal_manager.signal_mask, SIGCHLD);
    sigaddset(&g_signal_manager.signal_mask, SIGUSR1);
    sigaddset(&g_signal_manager.signal_mask, SIGUSR2);

    /* 阻塞这些信号，让信号线程处理 */
    if (pthread_sigmask(SIG_BLOCK, &g_signal_manager.signal_mask, NULL) != 0) {
        qrzl_log_error("Failed to block signals: %s", strerror(errno));
        pthread_mutex_destroy(&g_signal_manager.mutex);
        return QRZL_ERROR;
    }

    /* 创建信号处理线程 */
    if (pthread_create(&g_signal_manager.signal_thread, NULL, qrzl_signal_thread_func, NULL) != 0) {
        qrzl_log_error("Failed to create signal thread: %s", strerror(errno));
        pthread_mutex_destroy(&g_signal_manager.mutex);
        return QRZL_ERROR;
    }

    g_signal_manager.initialized = true;
    qrzl_log_info("Signal handler system initialized");
    return QRZL_SUCCESS;
}

/* 销毁信号处理系统 */
void qrzl_signal_destroy(void) {
    if (!g_signal_manager.initialized) {
        return;
    }

    /* 发送退出信号给信号线程 */
    pthread_kill(g_signal_manager.signal_thread, SIGUSR1);
    pthread_join(g_signal_manager.signal_thread, NULL);

    /* 清理信号处理器链表 */
    pthread_mutex_lock(&g_signal_manager.mutex);
    qrzl_signal_handler_t *handler = g_signal_manager.handler_list;
    while (handler) {
        qrzl_signal_handler_t *next = handler->next;
        QRZL_FREE(handler);
        handler = next;
    }
    g_signal_manager.handler_list = NULL;
    pthread_mutex_unlock(&g_signal_manager.mutex);

    pthread_mutex_destroy(&g_signal_manager.mutex);
    g_signal_manager.initialized = false;
    
    qrzl_log_info("Signal handler system destroyed");
}

/* 注册信号处理器 */
int qrzl_signal_register(int signum, qrzl_signal_callback_t callback, void *user_data) {
    if (!callback) {
        return QRZL_INVALID_PARAM;
    }

    qrzl_signal_handler_t *handler = QRZL_CALLOC(1, sizeof(qrzl_signal_handler_t));
    if (!handler) {
        qrzl_log_error("Failed to allocate signal handler memory");
        return QRZL_NO_MEMORY;
    }

    handler->signum = signum;
    handler->callback = callback;
    handler->user_data = user_data;

    pthread_mutex_lock(&g_signal_manager.mutex);
    
    /* 检查是否已经注册了该信号 */
    qrzl_signal_handler_t *existing = g_signal_manager.handler_list;
    while (existing) {
        if (existing->signum == signum) {
            pthread_mutex_unlock(&g_signal_manager.mutex);
            QRZL_FREE(handler);
            qrzl_log_warn("Signal %d already registered", signum);
            return QRZL_ERROR;
        }
        existing = existing->next;
    }

    /* 添加到链表头部 */
    handler->next = g_signal_manager.handler_list;
    g_signal_manager.handler_list = handler;

    /* 将信号添加到掩码中 */
    sigaddset(&g_signal_manager.signal_mask, signum);
    pthread_sigmask(SIG_BLOCK, &g_signal_manager.signal_mask, NULL);

    pthread_mutex_unlock(&g_signal_manager.mutex);

    qrzl_log_debug("Signal %d registered", signum);
    return QRZL_SUCCESS;
}

/* 注销信号处理器 */
int qrzl_signal_unregister(int signum) {
    pthread_mutex_lock(&g_signal_manager.mutex);
    
    qrzl_signal_handler_t **handler_ptr = &g_signal_manager.handler_list;
    while (*handler_ptr) {
        if ((*handler_ptr)->signum == signum) {
            qrzl_signal_handler_t *handler = *handler_ptr;
            *handler_ptr = handler->next;
            QRZL_FREE(handler);
            
            /* 从信号掩码中移除 */
            sigdelset(&g_signal_manager.signal_mask, signum);
            pthread_sigmask(SIG_UNBLOCK, &g_signal_manager.signal_mask, NULL);
            
            pthread_mutex_unlock(&g_signal_manager.mutex);
            qrzl_log_debug("Signal %d unregistered", signum);
            return QRZL_SUCCESS;
        }
        handler_ptr = &(*handler_ptr)->next;
    }
    
    pthread_mutex_unlock(&g_signal_manager.mutex);
    qrzl_log_warn("Signal %d not found", signum);
    return QRZL_ERROR;
}

/* 检查是否收到退出信号 */
bool qrzl_signal_is_shutdown_requested(void) {
    return g_qrzl_shutdown_flag;
}

/* 设置退出标志 */
void qrzl_signal_set_shutdown_flag(void) {
    g_qrzl_shutdown_flag = 1;
    g_qrzl_app_state = QRZL_APP_STATE_STOPPING;
}

/* 清除退出标志 */
void qrzl_signal_clear_shutdown_flag(void) {
    g_qrzl_shutdown_flag = 0;
}

/* 默认SIGINT处理函数 */
void qrzl_signal_default_sigint_handler(int signum, void *user_data) {
    (void)signum;    /* 避免未使用参数警告 */
    (void)user_data; /* 避免未使用参数警告 */
    qrzl_log_info("Received SIGINT, initiating graceful shutdown...");
    qrzl_signal_set_shutdown_flag();
}

/* 默认SIGTERM处理函数 */
void qrzl_signal_default_sigterm_handler(int signum, void *user_data) {
    (void)signum;    /* 避免未使用参数警告 */
    (void)user_data; /* 避免未使用参数警告 */
    qrzl_log_info("Received SIGTERM, initiating graceful shutdown...");
    qrzl_signal_set_shutdown_flag();
}

/* 默认SIGPIPE处理函数 */
void qrzl_signal_default_sigpipe_handler(int signum, void *user_data) {
    (void)signum;    /* 避免未使用参数警告 */
    (void)user_data; /* 避免未使用参数警告 */
    qrzl_log_warn("Received SIGPIPE, ignoring...");
}

/* 默认SIGCHLD处理函数 */
void qrzl_signal_default_sigchld_handler(int signum, void *user_data) {
    (void)signum;    /* 避免未使用参数警告 */
    (void)user_data; /* 避免未使用参数警告 */
    qrzl_log_debug("Received SIGCHLD");
}

/* 注册默认信号处理器 */
int qrzl_signal_register_defaults(void) {
    int ret = QRZL_SUCCESS;
    
    ret |= qrzl_signal_register(SIGINT, qrzl_signal_default_sigint_handler, NULL);
    ret |= qrzl_signal_register(SIGTERM, qrzl_signal_default_sigterm_handler, NULL);
    ret |= qrzl_signal_register(SIGPIPE, qrzl_signal_default_sigpipe_handler, NULL);
    ret |= qrzl_signal_register(SIGCHLD, qrzl_signal_default_sigchld_handler, NULL);
    
    if (ret == QRZL_SUCCESS) {
        qrzl_log_info("Default signal handlers registered");
    } else {
        qrzl_log_error("Failed to register some default signal handlers");
    }
    
    return ret;
}

/* 优雅退出处理 */
void qrzl_signal_graceful_shutdown(void) {
    qrzl_log_info("Starting graceful shutdown...");
    
    /* 停止所有线程 */
    qrzl_thread_stop_all(5000); /* 5秒超时 */
    
    /* 销毁定时器系统 */
    qrzl_timer_destroy();
    
    /* 销毁线程管理器 */
    qrzl_thread_mgr_destroy();
    
    qrzl_log_info("Graceful shutdown completed");
}

/* 获取信号名称 */
const char* qrzl_signal_get_name(int signum) {
    switch (signum) {
        case SIGINT:  return "SIGINT";
        case SIGTERM: return "SIGTERM";
        case SIGPIPE: return "SIGPIPE";
        case SIGCHLD: return "SIGCHLD";
        case SIGUSR1: return "SIGUSR1";
        case SIGUSR2: return "SIGUSR2";
        default:      return "UNKNOWN";
    }
}

/* 信号线程函数 */
static void* qrzl_signal_thread_func(void *arg) {
    (void)arg; /* 避免未使用参数警告 */
    int signum;

    qrzl_log_info("Signal thread started");

    while (true) {
        /* 等待信号 */
        if (sigwait(&g_signal_manager.signal_mask, &signum) != 0) {
            qrzl_log_error("sigwait failed: %s", strerror(errno));
            continue;
        }

        qrzl_log_debug("Received signal %s (%d)", qrzl_signal_get_name(signum), signum);

        /* 查找并调用对应的处理函数 */
        pthread_mutex_lock(&g_signal_manager.mutex);

        qrzl_signal_handler_t *handler = g_signal_manager.handler_list;
        bool found = false;

        while (handler) {
            if (handler->signum == signum) {
                found = true;
                /* 解锁后调用回调函数 */
                pthread_mutex_unlock(&g_signal_manager.mutex);

                if (handler->callback) {
                    handler->callback(signum, handler->user_data);
                }

                pthread_mutex_lock(&g_signal_manager.mutex);
                break;
            }
            handler = handler->next;
        }

        pthread_mutex_unlock(&g_signal_manager.mutex);

        if (!found) {
            qrzl_log_warn("No handler found for signal %s (%d)",
                         qrzl_signal_get_name(signum), signum);
        }

        /* 检查是否是退出信号 */
        if (signum == SIGINT || signum == SIGTERM) {
            break;
        }

        /* SIGUSR1用于退出信号线程 */
        if (signum == SIGUSR1 && !g_signal_manager.initialized) {
            break;
        }
    }

    qrzl_log_info("Signal thread ended");
    return NULL;
}

/* 等待退出信号 */
bool qrzl_signal_wait_for_shutdown(uint32_t timeout_ms) {
    uint64_t start_time = qrzl_timer_get_timestamp_ms();

    while (!qrzl_signal_is_shutdown_requested()) {
        if (timeout_ms > 0) {
            uint64_t current_time = qrzl_timer_get_timestamp_ms();
            if (current_time - start_time >= timeout_ms) {
                return false; /* 超时 */
            }
        }

        qrzl_timer_sleep_ms(100); /* 100ms检查间隔 */
    }

    return true;
}

/* 阻塞等待信号 */
int qrzl_signal_wait(uint32_t timeout_ms) {
    sigset_t wait_mask;
    int signum;

    sigemptyset(&wait_mask);
    sigaddset(&wait_mask, SIGINT);
    sigaddset(&wait_mask, SIGTERM);
    sigaddset(&wait_mask, SIGUSR1);
    sigaddset(&wait_mask, SIGUSR2);

    if (timeout_ms == 0) {
        /* 无限等待 */
        if (sigwait(&wait_mask, &signum) == 0) {
            return signum;
        }
    } else {
        /* 带超时等待 */
        struct timespec timeout;
        timeout.tv_sec = timeout_ms / 1000;
        timeout.tv_nsec = (timeout_ms % 1000) * 1000000;

        if (sigtimedwait(&wait_mask, NULL, &timeout) > 0) {
            return signum;
        }
    }

    return -1;
}

/* 发送信号给自己 */
int qrzl_signal_send_self(int signum) {
    if (kill(getpid(), signum) == 0) {
        return QRZL_SUCCESS;
    }

    qrzl_log_error("Failed to send signal %d: %s", signum, strerror(errno));
    return QRZL_ERROR;
}

/* 屏蔽信号 */
int qrzl_signal_block(int signum) {
    sigset_t mask;
    sigemptyset(&mask);
    sigaddset(&mask, signum);

    if (pthread_sigmask(SIG_BLOCK, &mask, NULL) == 0) {
        return QRZL_SUCCESS;
    }

    qrzl_log_error("Failed to block signal %d: %s", signum, strerror(errno));
    return QRZL_ERROR;
}

/* 解除信号屏蔽 */
int qrzl_signal_unblock(int signum) {
    sigset_t mask;
    sigemptyset(&mask);
    sigaddset(&mask, signum);

    if (pthread_sigmask(SIG_UNBLOCK, &mask, NULL) == 0) {
        return QRZL_SUCCESS;
    }

    qrzl_log_error("Failed to unblock signal %d: %s", signum, strerror(errno));
    return QRZL_ERROR;
}
