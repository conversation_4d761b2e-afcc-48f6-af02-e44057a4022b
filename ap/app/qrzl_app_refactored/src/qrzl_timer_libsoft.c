#include "../inc/qrzl_timer.h"
#include <sys/time.h>
#include <errno.h>

/* 定时器信息结构 */
typedef struct qrzl_timer_info {
    qrzl_timer_id_t id;
    qrzl_timer_type_t type;
    qrzl_timer_state_t state;
    uint32_t interval_ms;
    qrzl_timer_callback_t callback;
    void *user_data;
    struct qrzl_timer_info *next;
} qrzl_timer_info_t;

/* 全局定时器管理器 */
static struct {
    bool initialized;
    pthread_mutex_t mutex;
    qrzl_timer_id_t next_timer_id;
    qrzl_timer_info_t *timer_list;
} g_timer_manager = {0};

/* 查找定时器 */
static qrzl_timer_t* find_timer(qrzl_timer_id_t timer_id);

/* libsoft_timer回调包装器 */
static void* qrzl_soft_timer_wrapper(void *arg) {
    qrzl_timer_info_t *timer_info = (qrzl_timer_info_t *)arg;
    
    if (timer_info && timer_info->callback) {
        qrzl_log_debug("Timer %u callback triggered", timer_info->id);
        timer_info->callback(timer_info->id, timer_info->user_data);
        
        /* 如果是一次性定时器，更新状态 */
        if (timer_info->type == QRZL_TIMER_ONESHOT) {
            pthread_mutex_lock(&g_timer_manager.mutex);
            timer_info->state = QRZL_TIMER_STATE_EXPIRED;
            pthread_mutex_unlock(&g_timer_manager.mutex);
        }
    }
    
    return NULL;
}

/* 查找定时器信息 */
static qrzl_timer_info_t* find_timer_info(qrzl_timer_id_t timer_id) {
    qrzl_timer_info_t *info = g_timer_manager.timer_list;
    while (info) {
        if (info->id == timer_id) {
            return info;
        }
        info = info->next;
    }
    return NULL;
}

/* 查找定时器实现 */
static qrzl_timer_t* find_timer(qrzl_timer_id_t timer_id) {
    /* 在测试环境下，我们简化实现 */
    qrzl_timer_info_t *info = find_timer_info(timer_id);
    if (info) {
        /* 创建一个临时的qrzl_timer_t结构 */
        static qrzl_timer_t temp_timer;
        temp_timer.id = info->id;
        temp_timer.type = info->type;
        temp_timer.state = info->state;
        temp_timer.interval_ms = info->interval_ms;
        temp_timer.callback = info->callback;
        temp_timer.user_data = info->user_data;
        return &temp_timer;
    }
    return NULL;
}

/* 初始化定时器系统 */
int qrzl_timer_init(void) {
    if (g_timer_manager.initialized) {
        return QRZL_SUCCESS;
    }

    memset(&g_timer_manager, 0, sizeof(g_timer_manager));
    
    if (pthread_mutex_init(&g_timer_manager.mutex, NULL) != 0) {
        qrzl_log_error("Failed to init timer manager mutex: %s", strerror(errno));
        return QRZL_ERROR;
    }

    g_timer_manager.next_timer_id = 1;
    g_timer_manager.initialized = true;

    qrzl_log_info("Timer system initialized successfully");
    return QRZL_SUCCESS;
}

/* 销毁定时器系统 */
void qrzl_timer_destroy(void) {
    if (!g_timer_manager.initialized) {
        return;
    }

    /* 停止所有定时器 */
    pthread_mutex_lock(&g_timer_manager.mutex);
    qrzl_timer_info_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->state == QRZL_TIMER_STATE_RUNNING) {
            DeleteSoftTimer((USHORT)timer->id);
        }
        qrzl_timer_info_t *next = timer->next;
        QRZL_FREE(timer);
        timer = next;
    }
    g_timer_manager.timer_list = NULL;
    pthread_mutex_unlock(&g_timer_manager.mutex);

    pthread_mutex_destroy(&g_timer_manager.mutex);
    g_timer_manager.initialized = false;
    
    qrzl_log_info("Timer system destroyed");
}

/* 创建定时器 */
qrzl_timer_id_t qrzl_timer_create(qrzl_timer_type_t type, 
                                  uint32_t interval_ms,
                                  qrzl_timer_callback_t callback,
                                  void *user_data) {
    if (!callback || interval_ms == 0) {
        qrzl_log_error("Invalid timer parameters");
        return QRZL_INVALID_TIMER_ID;
    }

    qrzl_timer_info_t *timer = QRZL_CALLOC(1, sizeof(qrzl_timer_info_t));
    if (!timer) {
        qrzl_log_error("Failed to allocate timer memory");
        return QRZL_INVALID_TIMER_ID;
    }

    pthread_mutex_lock(&g_timer_manager.mutex);
    
    timer->id = g_timer_manager.next_timer_id++;
    timer->type = type;
    timer->state = QRZL_TIMER_STATE_IDLE;
    timer->interval_ms = interval_ms;
    timer->callback = callback;
    timer->user_data = user_data;

    /* 添加到链表头部 */
    timer->next = g_timer_manager.timer_list;
    g_timer_manager.timer_list = timer;

    pthread_mutex_unlock(&g_timer_manager.mutex);

    qrzl_log_debug("Timer %u created (type=%d, interval=%ums)", 
                   timer->id, type, interval_ms);
    return timer->id;
}

/* 启动定时器 */
int qrzl_timer_start(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_info_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            if (timer->state == QRZL_TIMER_STATE_RUNNING) {
                pthread_mutex_unlock(&g_timer_manager.mutex);
                qrzl_log_warn("Timer %u is already running", timer_id);
                return QRZL_SUCCESS;
            }
            
            /* 使用libsoft_timer创建定时器 */
            UCHAR flag = (timer->type == QRZL_TIMER_PERIODIC) ? TIMER_FLAG_RESTART : TIMER_FLAG_ONCE;
            LONG ret = CreateSoftTimer((USHORT)timer_id, flag, timer->interval_ms, 
                                      qrzl_soft_timer_wrapper, timer);
            
            if (ret != 0) {
                pthread_mutex_unlock(&g_timer_manager.mutex);
                qrzl_log_error("Failed to create soft timer %u, ret=%ld", timer_id, ret);
                return QRZL_ERROR;
            }
            
            timer->state = QRZL_TIMER_STATE_RUNNING;
            
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u started", timer_id);
            return QRZL_SUCCESS;
        }
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 停止定时器 */
int qrzl_timer_stop(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_info_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            if (timer->state == QRZL_TIMER_STATE_RUNNING) {
                DeleteSoftTimer((USHORT)timer_id);
                timer->state = QRZL_TIMER_STATE_CANCELLED;
            }
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u stopped", timer_id);
            return QRZL_SUCCESS;
        }
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 删除定时器 */
int qrzl_timer_delete(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_info_t *timer = g_timer_manager.timer_list;
    qrzl_timer_info_t *prev = NULL;
    
    while (timer) {
        if (timer->id == timer_id) {
            /* 先停止定时器 */
            if (timer->state == QRZL_TIMER_STATE_RUNNING) {
                DeleteSoftTimer((USHORT)timer_id);
            }
            
            /* 从链表中移除 */
            if (prev) {
                prev->next = timer->next;
            } else {
                g_timer_manager.timer_list = timer->next;
            }
            
            QRZL_FREE(timer);
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u deleted", timer_id);
            return QRZL_SUCCESS;
        }
        prev = timer;
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 重置定时器 */
int qrzl_timer_reset(qrzl_timer_id_t timer_id, uint32_t interval_ms) {
    qrzl_timer_t *timer = find_timer(timer_id);
    if (!timer) {
        return QRZL_ERROR;
    }

    /* 更新间隔时间 */
    timer->interval_ms = interval_ms;

    /* 先停止再启动 */
    qrzl_timer_stop(timer_id);
    return qrzl_timer_start(timer_id);
}

/* 获取定时器状态 */
qrzl_timer_state_t qrzl_timer_get_state(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_info_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            qrzl_timer_state_t state = timer->state;
            pthread_mutex_unlock(&g_timer_manager.mutex);
            return state;
        }
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    return QRZL_TIMER_STATE_IDLE;
}

/* 延迟执行函数 */
qrzl_timer_id_t qrzl_timer_delay(uint32_t ms, 
                                 qrzl_timer_callback_t callback,
                                 void *user_data) {
    qrzl_timer_id_t timer_id = qrzl_timer_create(QRZL_TIMER_ONESHOT, ms, callback, user_data);
    if (timer_id != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_start(timer_id);
    }
    return timer_id;
}

/* 周期性执行函数 */
qrzl_timer_id_t qrzl_timer_periodic(uint32_t interval_ms,
                                    qrzl_timer_callback_t callback,
                                    void *user_data) {
    qrzl_timer_id_t timer_id = qrzl_timer_create(QRZL_TIMER_PERIODIC, interval_ms, callback, user_data);
    if (timer_id != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_start(timer_id);
    }
    return timer_id;
}

/* 毫秒级睡眠 */
void qrzl_timer_sleep_ms(uint32_t ms) {
    struct timespec ts;
    ts.tv_sec = ms / 1000;
    ts.tv_nsec = (ms % 1000) * 1000000;
    nanosleep(&ts, NULL);
}

/* 秒级睡眠 */
void qrzl_timer_sleep(uint32_t seconds) {
    qrzl_timer_sleep_ms(seconds * 1000);
}

/* 获取当前时间戳(毫秒) */
uint64_t qrzl_timer_get_timestamp_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}
