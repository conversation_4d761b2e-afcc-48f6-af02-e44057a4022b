#include "../inc/qrzl_timer.h"
#include <sys/time.h>
#include <errno.h>

/* 全局定时器管理器 */
static qrzl_timer_manager_t g_timer_manager = {0};

/* libsoft_timer回调包装器 */
static void* qrzl_soft_timer_wrapper(void *arg);

/* 定时器线程函数 */
static void* qrzl_timer_thread_func(void *arg);

/* 获取当前时间戳(毫秒) */
uint64_t qrzl_timer_get_timestamp_ms(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

/* 初始化定时器系统 */
int qrzl_timer_init(void) {
    if (g_timer_manager.running) {
        return QRZL_SUCCESS;
    }

    memset(&g_timer_manager, 0, sizeof(g_timer_manager));
    
    if (pthread_mutex_init(&g_timer_manager.mutex, NULL) != 0) {
        qrzl_log_error("Failed to init timer mutex: %s", strerror(errno));
        return QRZL_ERROR;
    }

    if (pthread_cond_init(&g_timer_manager.cond, NULL) != 0) {
        qrzl_log_error("Failed to init timer condition: %s", strerror(errno));
        pthread_mutex_destroy(&g_timer_manager.mutex);
        return QRZL_ERROR;
    }

    g_timer_manager.next_timer_id = 1;
    g_timer_manager.running = true;

    if (pthread_create(&g_timer_manager.thread_id, NULL, qrzl_timer_thread_func, NULL) != 0) {
        qrzl_log_error("Failed to create timer thread: %s", strerror(errno));
        pthread_cond_destroy(&g_timer_manager.cond);
        pthread_mutex_destroy(&g_timer_manager.mutex);
        g_timer_manager.running = false;
        return QRZL_ERROR;
    }

    qrzl_log_info("Timer system initialized successfully");
    return QRZL_SUCCESS;
}

/* 销毁定时器系统 */
void qrzl_timer_destroy(void) {
    if (!g_timer_manager.running) {
        return;
    }

    pthread_mutex_lock(&g_timer_manager.mutex);
    g_timer_manager.running = false;
    pthread_cond_broadcast(&g_timer_manager.cond);
    pthread_mutex_unlock(&g_timer_manager.mutex);

    pthread_join(g_timer_manager.thread_id, NULL);

    /* 清理所有定时器 */
    qrzl_timer_t *timer = g_timer_manager.timer_list;
    while (timer) {
        qrzl_timer_t *next = timer->next;
        QRZL_FREE(timer);
        timer = next;
    }

    pthread_cond_destroy(&g_timer_manager.cond);
    pthread_mutex_destroy(&g_timer_manager.mutex);
    memset(&g_timer_manager, 0, sizeof(g_timer_manager));

    qrzl_log_info("Timer system destroyed");
}

/* 创建定时器 */
qrzl_timer_id_t qrzl_timer_create(qrzl_timer_type_t type, 
                                  uint32_t interval_ms,
                                  qrzl_timer_callback_t callback,
                                  void *user_data) {
    if (!callback || interval_ms == 0) {
        qrzl_log_error("Invalid timer parameters");
        return QRZL_INVALID_TIMER_ID;
    }

    qrzl_timer_t *timer = QRZL_CALLOC(1, sizeof(qrzl_timer_t));
    if (!timer) {
        qrzl_log_error("Failed to allocate timer memory");
        return QRZL_INVALID_TIMER_ID;
    }

    pthread_mutex_lock(&g_timer_manager.mutex);
    
    timer->id = g_timer_manager.next_timer_id++;
    timer->type = type;
    timer->state = QRZL_TIMER_STATE_IDLE;
    timer->interval_ms = interval_ms;
    timer->remaining_ms = interval_ms;
    timer->callback = callback;
    timer->user_data = user_data;

    /* 添加到链表头部 */
    timer->next = g_timer_manager.timer_list;
    g_timer_manager.timer_list = timer;

    pthread_mutex_unlock(&g_timer_manager.mutex);

    qrzl_log_debug("Timer %u created (type=%d, interval=%ums)", 
                   timer->id, type, interval_ms);
    return timer->id;
}

/* 启动定时器 */
int qrzl_timer_start(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            timer->state = QRZL_TIMER_STATE_RUNNING;
            timer->remaining_ms = timer->interval_ms;
            pthread_cond_signal(&g_timer_manager.cond);
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u started", timer_id);
            return QRZL_SUCCESS;
        }
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 停止定时器 */
int qrzl_timer_stop(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            timer->state = QRZL_TIMER_STATE_CANCELLED;
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u stopped", timer_id);
            return QRZL_SUCCESS;
        }
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 删除定时器 */
int qrzl_timer_delete(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_t **timer_ptr = &g_timer_manager.timer_list;
    while (*timer_ptr) {
        if ((*timer_ptr)->id == timer_id) {
            qrzl_timer_t *timer = *timer_ptr;
            *timer_ptr = timer->next;
            QRZL_FREE(timer);
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u deleted", timer_id);
            return QRZL_SUCCESS;
        }
        timer_ptr = &(*timer_ptr)->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 获取定时器状态 */
qrzl_timer_state_t qrzl_timer_get_state(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);
    
    qrzl_timer_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            qrzl_timer_state_t state = timer->state;
            pthread_mutex_unlock(&g_timer_manager.mutex);
            return state;
        }
        timer = timer->next;
    }
    
    pthread_mutex_unlock(&g_timer_manager.mutex);
    return QRZL_TIMER_STATE_IDLE;
}

/* 延迟执行函数 */
qrzl_timer_id_t qrzl_timer_delay(uint32_t ms, 
                                 qrzl_timer_callback_t callback,
                                 void *user_data) {
    qrzl_timer_id_t timer_id = qrzl_timer_create(QRZL_TIMER_ONESHOT, ms, callback, user_data);
    if (timer_id != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_start(timer_id);
    }
    return timer_id;
}

/* 周期性执行函数 */
qrzl_timer_id_t qrzl_timer_periodic(uint32_t interval_ms,
                                    qrzl_timer_callback_t callback,
                                    void *user_data) {
    qrzl_timer_id_t timer_id = qrzl_timer_create(QRZL_TIMER_PERIODIC, interval_ms, callback, user_data);
    if (timer_id != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_start(timer_id);
    }
    return timer_id;
}

/* 毫秒级睡眠 */
void qrzl_timer_sleep_ms(uint32_t ms) {
    struct timespec ts;
    ts.tv_sec = ms / 1000;
    ts.tv_nsec = (ms % 1000) * 1000000;
    nanosleep(&ts, NULL);
}

/* 秒级睡眠 */
void qrzl_timer_sleep(uint32_t seconds) {
    qrzl_timer_sleep_ms(seconds * 1000);
}

/* 定时器线程函数 */
static void* qrzl_timer_thread_func(void *arg) {
    const uint32_t tick_interval_ms = 100; /* 100ms tick */
    struct timespec timeout;

    qrzl_log_info("Timer thread started");

    while (g_timer_manager.running) {
        pthread_mutex_lock(&g_timer_manager.mutex);

        /* 计算下次唤醒时间 */
        clock_gettime(CLOCK_REALTIME, &timeout);
        timeout.tv_nsec += tick_interval_ms * 1000000;
        if (timeout.tv_nsec >= 1000000000) {
            timeout.tv_sec++;
            timeout.tv_nsec -= 1000000000;
        }

        /* 等待超时或条件信号 */
        pthread_cond_timedwait(&g_timer_manager.cond, &g_timer_manager.mutex, &timeout);

        if (!g_timer_manager.running) {
            pthread_mutex_unlock(&g_timer_manager.mutex);
            break;
        }

        /* 处理所有运行中的定时器 */
        qrzl_timer_t *timer = g_timer_manager.timer_list;
        while (timer) {
            if (timer->state == QRZL_TIMER_STATE_RUNNING) {
                if (timer->remaining_ms <= tick_interval_ms) {
                    /* 定时器到期 */
                    timer->state = QRZL_TIMER_STATE_EXPIRED;

                    /* 解锁后调用回调函数 */
                    pthread_mutex_unlock(&g_timer_manager.mutex);

                    if (timer->callback) {
                        timer->callback(timer->id, timer->user_data);
                    }

                    pthread_mutex_lock(&g_timer_manager.mutex);

                    /* 重新查找定时器(可能在回调中被删除) */
                    qrzl_timer_t *current = g_timer_manager.timer_list;
                    while (current && current->id != timer->id) {
                        current = current->next;
                    }

                    if (current) {
                        if (current->type == QRZL_TIMER_PERIODIC) {
                            /* 周期性定时器重新启动 */
                            current->remaining_ms = current->interval_ms;
                            current->state = QRZL_TIMER_STATE_RUNNING;
                        } else {
                            /* 一次性定时器标记为完成 */
                            current->state = QRZL_TIMER_STATE_EXPIRED;
                        }
                    }
                } else {
                    timer->remaining_ms -= tick_interval_ms;
                }
            }
            timer = timer->next;
        }

        pthread_mutex_unlock(&g_timer_manager.mutex);
    }

    qrzl_log_info("Timer thread ended");
    return NULL;
}

/* 重置定时器 */
int qrzl_timer_reset(qrzl_timer_id_t timer_id, uint32_t interval_ms) {
    if (interval_ms == 0) {
        return QRZL_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_timer_manager.mutex);

    qrzl_timer_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            timer->interval_ms = interval_ms;
            timer->remaining_ms = interval_ms;
            if (timer->state == QRZL_TIMER_STATE_EXPIRED ||
                timer->state == QRZL_TIMER_STATE_CANCELLED) {
                timer->state = QRZL_TIMER_STATE_IDLE;
            }
            pthread_mutex_unlock(&g_timer_manager.mutex);
            qrzl_log_debug("Timer %u reset to %ums", timer_id, interval_ms);
            return QRZL_SUCCESS;
        }
        timer = timer->next;
    }

    pthread_mutex_unlock(&g_timer_manager.mutex);
    qrzl_log_error("Timer %u not found", timer_id);
    return QRZL_ERROR;
}

/* 获取定时器剩余时间 */
uint32_t qrzl_timer_get_remaining(qrzl_timer_id_t timer_id) {
    pthread_mutex_lock(&g_timer_manager.mutex);

    qrzl_timer_t *timer = g_timer_manager.timer_list;
    while (timer) {
        if (timer->id == timer_id) {
            uint32_t remaining = timer->remaining_ms;
            pthread_mutex_unlock(&g_timer_manager.mutex);
            return remaining;
        }
        timer = timer->next;
    }

    pthread_mutex_unlock(&g_timer_manager.mutex);
    return 0;
}
