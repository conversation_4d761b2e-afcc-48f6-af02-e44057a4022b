#include "../inc/qrzl_thread_mgr.h"
#include "../inc/qrzl_timer.h"

/* 全局线程管理器 */
static qrzl_thread_manager_t g_thread_manager = {0};

/* 线程包装函数 */
static void* qrzl_thread_wrapper(void *arg);

/* 初始化线程管理器 */
int qrzl_thread_mgr_init(void) {
    if (g_thread_manager.initialized) {
        return QRZL_SUCCESS;
    }

    memset(&g_thread_manager, 0, sizeof(g_thread_manager));
    
    if (pthread_mutex_init(&g_thread_manager.mutex, NULL) != 0) {
        qrzl_log_error("Failed to init thread manager mutex: %s", strerror(errno));
        return QRZL_ERROR;
    }

    g_thread_manager.next_thread_id = 1;
    g_thread_manager.initialized = true;

    qrzl_log_info("Thread manager initialized");
    return QRZL_SUCCESS;
}

/* 销毁线程管理器 */
void qrzl_thread_mgr_destroy(void) {
    if (!g_thread_manager.initialized) {
        return;
    }

    /* 停止所有线程 */
    qrzl_thread_stop_all(5000);

    /* 清理线程信息链表 */
    pthread_mutex_lock(&g_thread_manager.mutex);
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        qrzl_thread_info_t *next = thread->next;
        QRZL_FREE(thread);
        thread = next;
    }
    g_thread_manager.thread_list = NULL;
    pthread_mutex_unlock(&g_thread_manager.mutex);

    pthread_mutex_destroy(&g_thread_manager.mutex);
    g_thread_manager.initialized = false;
    
    qrzl_log_info("Thread manager destroyed");
}

/* 创建线程 */
qrzl_thread_id_t qrzl_thread_create(qrzl_thread_type_t type,
                                    const char *name,
                                    qrzl_thread_func_t func,
                                    void *arg) {
    if (!func || !name) {
        qrzl_log_error("Invalid thread parameters");
        return QRZL_INVALID_THREAD_ID;
    }

    qrzl_thread_info_t *thread = QRZL_CALLOC(1, sizeof(qrzl_thread_info_t));
    if (!thread) {
        qrzl_log_error("Failed to allocate thread info memory");
        return QRZL_INVALID_THREAD_ID;
    }

    pthread_mutex_lock(&g_thread_manager.mutex);
    
    thread->id = g_thread_manager.next_thread_id++;
    thread->type = type;
    thread->state = QRZL_THREAD_STATE_IDLE;
    thread->func = func;
    thread->arg = arg;
    thread->create_time = time(NULL);
    thread->should_stop = false;
    QRZL_STRNCPY(thread->name, name, sizeof(thread->name));

    /* 添加到链表头部 */
    thread->next = g_thread_manager.thread_list;
    g_thread_manager.thread_list = thread;
    g_thread_manager.thread_count++;

    pthread_mutex_unlock(&g_thread_manager.mutex);

    qrzl_log_debug("Thread %u (%s) created", thread->id, name);
    return thread->id;
}

/* 启动线程 */
int qrzl_thread_start(qrzl_thread_id_t thread_id) {
    pthread_mutex_lock(&g_thread_manager.mutex);
    
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        if (thread->id == thread_id) {
            if (thread->state != QRZL_THREAD_STATE_IDLE) {
                pthread_mutex_unlock(&g_thread_manager.mutex);
                qrzl_log_warn("Thread %u is not in idle state", thread_id);
                return QRZL_ERROR;
            }
            
            thread->state = QRZL_THREAD_STATE_RUNNING;
            thread->start_time = time(NULL);
            
            if (pthread_create(&thread->pthread_id, NULL, qrzl_thread_wrapper, thread) != 0) {
                thread->state = QRZL_THREAD_STATE_ERROR;
                pthread_mutex_unlock(&g_thread_manager.mutex);
                qrzl_log_error("Failed to create pthread for thread %u: %s", 
                              thread_id, strerror(errno));
                return QRZL_ERROR;
            }
            
            pthread_mutex_unlock(&g_thread_manager.mutex);
            qrzl_log_debug("Thread %u (%s) started", thread_id, thread->name);
            return QRZL_SUCCESS;
        }
        thread = thread->next;
    }
    
    pthread_mutex_unlock(&g_thread_manager.mutex);
    qrzl_log_error("Thread %u not found", thread_id);
    return QRZL_ERROR;
}

/* 停止线程 */
int qrzl_thread_stop(qrzl_thread_id_t thread_id, uint32_t timeout_ms) {
    pthread_mutex_lock(&g_thread_manager.mutex);
    
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        if (thread->id == thread_id) {
            if (thread->state != QRZL_THREAD_STATE_RUNNING) {
                pthread_mutex_unlock(&g_thread_manager.mutex);
                return QRZL_SUCCESS; /* 已经停止 */
            }
            
            thread->should_stop = true;
            thread->state = QRZL_THREAD_STATE_STOPPING;
            pthread_t pthread_id = thread->pthread_id;
            
            pthread_mutex_unlock(&g_thread_manager.mutex);
            
            /* 等待线程结束 */
            if (timeout_ms == 0) {
                pthread_join(pthread_id, NULL);
            } else {
                struct timespec timeout;
                clock_gettime(CLOCK_REALTIME, &timeout);
                timeout.tv_sec += timeout_ms / 1000;
                timeout.tv_nsec += (timeout_ms % 1000) * 1000000;
                if (timeout.tv_nsec >= 1000000000) {
                    timeout.tv_sec++;
                    timeout.tv_nsec -= 1000000000;
                }
                
                if (pthread_timedjoin_np(pthread_id, NULL, &timeout) != 0) {
                    qrzl_log_warn("Thread %u stop timeout, forcing termination", thread_id);
                    pthread_cancel(pthread_id);
                    pthread_join(pthread_id, NULL);
                }
            }
            
            pthread_mutex_lock(&g_thread_manager.mutex);
            /* 重新查找线程(可能在等待期间被删除) */
            qrzl_thread_info_t *current = g_thread_manager.thread_list;
            while (current && current->id != thread_id) {
                current = current->next;
            }
            if (current) {
                current->state = QRZL_THREAD_STATE_STOPPED;
            }
            pthread_mutex_unlock(&g_thread_manager.mutex);
            
            qrzl_log_debug("Thread %u stopped", thread_id);
            return QRZL_SUCCESS;
        }
        thread = thread->next;
    }
    
    pthread_mutex_unlock(&g_thread_manager.mutex);
    qrzl_log_error("Thread %u not found", thread_id);
    return QRZL_ERROR;
}

/* 获取线程状态 */
qrzl_thread_state_t qrzl_thread_get_state(qrzl_thread_id_t thread_id) {
    pthread_mutex_lock(&g_thread_manager.mutex);
    
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        if (thread->id == thread_id) {
            qrzl_thread_state_t state = thread->state;
            pthread_mutex_unlock(&g_thread_manager.mutex);
            return state;
        }
        thread = thread->next;
    }
    
    pthread_mutex_unlock(&g_thread_manager.mutex);
    return QRZL_THREAD_STATE_ERROR;
}

/* 检查线程是否应该停止 */
bool qrzl_thread_should_stop(qrzl_thread_id_t thread_id) {
    pthread_mutex_lock(&g_thread_manager.mutex);
    
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        if (thread->id == thread_id) {
            bool should_stop = thread->should_stop;
            pthread_mutex_unlock(&g_thread_manager.mutex);
            return should_stop;
        }
        thread = thread->next;
    }
    
    pthread_mutex_unlock(&g_thread_manager.mutex);
    return true; /* 未找到线程，应该停止 */
}

/* 获取当前线程ID */
qrzl_thread_id_t qrzl_thread_get_current_id(void) {
    pthread_t current_pthread = pthread_self();
    
    pthread_mutex_lock(&g_thread_manager.mutex);
    
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        if (pthread_equal(thread->pthread_id, current_pthread)) {
            qrzl_thread_id_t id = thread->id;
            pthread_mutex_unlock(&g_thread_manager.mutex);
            return id;
        }
        thread = thread->next;
    }
    
    pthread_mutex_unlock(&g_thread_manager.mutex);
    return QRZL_INVALID_THREAD_ID;
}

/* 停止所有线程 */
int qrzl_thread_stop_all(uint32_t timeout_ms) {
    qrzl_log_info("Stopping all threads...");
    
    pthread_mutex_lock(&g_thread_manager.mutex);
    
    /* 设置所有线程的停止标志 */
    qrzl_thread_info_t *thread = g_thread_manager.thread_list;
    while (thread) {
        if (thread->state == QRZL_THREAD_STATE_RUNNING) {
            thread->should_stop = true;
            thread->state = QRZL_THREAD_STATE_STOPPING;
        }
        thread = thread->next;
    }
    
    pthread_mutex_unlock(&g_thread_manager.mutex);
    
    /* 等待所有线程结束 */
    uint64_t start_time = qrzl_timer_get_timestamp_ms();
    bool all_stopped = false;
    
    while (!all_stopped && (timeout_ms == 0 || 
           (qrzl_timer_get_timestamp_ms() - start_time) < timeout_ms)) {
        
        all_stopped = true;
        
        pthread_mutex_lock(&g_thread_manager.mutex);
        thread = g_thread_manager.thread_list;
        while (thread) {
            if (thread->state == QRZL_THREAD_STATE_STOPPING) {
                all_stopped = false;
                break;
            }
            thread = thread->next;
        }
        pthread_mutex_unlock(&g_thread_manager.mutex);
        
        if (!all_stopped) {
            qrzl_timer_sleep_ms(100);
        }
    }
    
    if (all_stopped) {
        qrzl_log_info("All threads stopped successfully");
        return QRZL_SUCCESS;
    } else {
        qrzl_log_warn("Some threads did not stop within timeout");
        return QRZL_TIMEOUT;
    }
}

/* 线程安全的睡眠函数 */
bool qrzl_thread_sleep_ms(qrzl_thread_id_t thread_id, uint32_t ms) {
    const uint32_t check_interval = 100; /* 100ms检查间隔 */
    uint32_t remaining = ms;
    
    while (remaining > 0) {
        if (qrzl_thread_should_stop(thread_id)) {
            return false; /* 被中断 */
        }
        
        uint32_t sleep_time = QRZL_MIN(remaining, check_interval);
        qrzl_timer_sleep_ms(sleep_time);
        remaining -= sleep_time;
    }
    
    return true;
}

/* 线程安全的秒级睡眠函数 */
bool qrzl_thread_sleep(qrzl_thread_id_t thread_id, uint32_t seconds) {
    return qrzl_thread_sleep_ms(thread_id, seconds * 1000);
}

/* 线程包装函数 */
static void* qrzl_thread_wrapper(void *arg) {
    qrzl_thread_info_t *thread = (qrzl_thread_info_t *)arg;
    void *result = NULL;
    
    /* 设置线程名称 */
    qrzl_set_thread_name(thread->name);
    
    qrzl_log_debug("Thread %u (%s) wrapper started", thread->id, thread->name);
    
    /* 调用实际的线程函数 */
    if (thread->func) {
        result = thread->func(thread->arg);
    }
    
    /* 更新线程状态 */
    pthread_mutex_lock(&g_thread_manager.mutex);
    thread->state = QRZL_THREAD_STATE_STOPPED;
    pthread_mutex_unlock(&g_thread_manager.mutex);
    
    qrzl_log_debug("Thread %u (%s) wrapper ended", thread->id, thread->name);
    return result;
}
