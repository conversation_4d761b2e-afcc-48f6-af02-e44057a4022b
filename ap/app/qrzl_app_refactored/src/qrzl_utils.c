/* 定义必要的宏（如果尚未定义） */
#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif
#ifndef _POSIX_C_SOURCE
#define _POSIX_C_SOURCE 200809L
#endif

/* 包含系统头文件 */
#include <sys/prctl.h>
#include <sys/time.h>
#include <sys/socket.h>
#include <string.h>
#include <unistd.h>
#include <ctype.h>
#include <stdlib.h>
#include <pthread.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <time.h>
#include <stdatomic.h>
#include <regex.h> // add by yangwei 2025-09-11 for is_valid_mac

/* 确保ENABLE_QRZL_APP宏被定义 */
#ifndef ENABLE_QRZL_APP
#define ENABLE_QRZL_APP
#endif

#include "nv_api.h"
#include "message.h"
#include "../inc/qrzl_utils.h"

/* 前向声明 */
#ifdef QRZL_AUTH_ONE_LINK_HTTP
extern void init_onelink_authed_info(void);
#endif

#ifdef QRZL_AUTH_CMP_HTTP
extern void init_cmp_authed_info(void);
#endif


static const unsigned char base64_table[65] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

// 上次wan口使用的上行流量 (暂时注释掉未使用的变量)
// static uint64_t last_wan_tx_bytes = 0;
// 上次wan口使用的下行流量 (暂时注释掉未使用的变量)
// static uint64_t last_wan_rx_bytes = 0;


/**
 * base64_decode - Base64 decode (暂时注释掉未使用的函数)
 * @src: Data to be decoded
 * @len: Length of the data to be decoded
 * @out_len: Pointer to output length variable
 * Returns: Allocated buffer of out_len bytes of decoded data,
 * or %NULL on failure
 *
 * Caller is responsible for freeing the returned buffer.
 */
#if 0
static unsigned char * qrzl_base64_decode(const unsigned char *src, size_t len,
                                  size_t *out_len)
{
	unsigned char dtable[256], *out, *pos, in[4], block[4], tmp;
	size_t i, count, olen;

	memset(dtable, 0x80, 256);
	for (i = 0; i < sizeof(base64_table) - 1; i++)
		dtable[base64_table[i]] = (unsigned char) i;
	dtable['='] = 0;

	count = 0;
	for (i = 0; i < len; i++) {
		if (dtable[src[i]] != 0x80)
			count++;
	}

	if (count == 0 || count % 4) 
        return NULL;
		
	olen = count / 4 * 3;
	pos = out = malloc(olen);
	if (out == NULL)
        return NULL;
		
	memset(pos, 0, olen);

	count = 0;
	for (i = 0; i < len; i++) {
		tmp = dtable[src[i]];
		if (tmp == 0x80)
			continue;

		in[count] = src[i];
		block[count] = tmp;
		count++;
		if (count == 4) {
			*pos++ = (block[0] << 2) | (block[1] >> 4);
			*pos++ = (block[1] << 4) | (block[2] >> 2);
			*pos++ = (block[2] << 6) | block[3];
			count = 0;
		}
	}

	if (pos > out) {
		if (in[2] == '=')
			pos -= 2;
		else if (in[3] == '=')
			pos--;
	}

	*out_len = pos - out;
	return out;
}
#endif

char *qrzl_base64_encode(const char *data, int data_len)
{ 
	int prepare = 0; 
	int ret_len; 
	int temp = 0; 
	char *ret = NULL; 
	char *f = NULL; 
	int tmp = 0; 
	char changed[4]; 
	int i = 0; 
	const char base[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="; 

	if(data == NULL)
	{
		return NULL;
	}
	if(data_len == 0)
	{
		return NULL;
	}
	ret_len = data_len / 3; 
	temp = data_len % 3; 
	if (temp > 0) 
	{ 
	ret_len += 1; 
	} 
	ret_len = ret_len*4 + 1; 
	ret = (char *)malloc(ret_len); 

	if (ret == NULL) 
	{ 
		qrzl_err("No enough memory.\n"); 
		return NULL;
	} 
	memset(ret, 0, ret_len); 
	f = ret; 
	while (tmp < data_len) 
	{ 
		temp = 0; 
		prepare = 0; 
		memset(changed, '\0', 4); 
		while (temp < 3) 
		{ 
			//printf("tmp = %d\n", tmp); 
			if (tmp >= data_len) 
			{ 
				break; 
			} 
			prepare = ((prepare << 8) | (data[tmp] & 0xFF)); 
			tmp++; 
			temp++; 
		} 
		prepare = (prepare<<((3-temp)*8)); 
		//printf("before for : temp = %d, prepare = %d\n", temp, prepare); 
		for (i = 0; i < 4 ;i++ ) 
		{ 
			if (temp < i) 
			{ 
				changed[i] = 0x40; 
			} 
			else 
			{ 
				changed[i] = (prepare>>((3-i)*6)) & 0x3F; 
			}
			*f = base[(unsigned char)changed[i]];
			//printf("%.2X", changed[i]); 
			f++; 	
		} 
	} 
	*f = '\0'; 
	return ret; 
}

int qrzl_base64_encode_safe(const char *data, int data_len, char *dest, size_t dest_len)
{
    char* encode = qrzl_base64_encode(data, data_len);
    if (encode == NULL)
    {
        return -1;
    }
    snprintf(dest, dest_len, "%s", encode);
    free(encode);
    return 0;
}

/**
 * 判断一个字符串是否是只包含英文字母加数字
 */
int is_alphanumeric(const char *str) {
    while (*str) {
        if (!isalnum((unsigned char)*str)) {
            return -1; // 如果字符不是字母或数字，返回0
        }
        str++;
    }
    return 0; // 如果所有字符都是字母或数字，返回1
}

static pthread_mutex_t switch_sim_card_lock;  // 定义切卡互斥锁，防止多线程同时切卡
static pthread_mutex_t update_device_dynamic_data_lock; // 更新设备动态数据锁，防止多线程环境下同时更新造成的异常

/* 这些全局变量已在qrzl_main.c中定义，这里使用extern声明 */
extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

void remove_spaces(char *str) {
    int i = 0, j = 0;
    while (str[i]) {
        if (str[i] != ' ') {
            str[j++] = str[i]; // 将非空格字符移动到前面
        }
        i++;
    }
    str[j] = '\0'; // 添加字符串结束符
}

/* 这些函数已在qrzl_main.c中定义，这里不重复定义 */

void qrzl_utils_init()
{
    /* 初始化全局结构体 */
    memset(&g_qrzl_device_static_data, 0, sizeof(g_qrzl_device_static_data));
    memset(&g_qrzl_device_dynamic_data, 0, sizeof(g_qrzl_device_dynamic_data));

    if (pthread_mutex_init(&switch_sim_card_lock, NULL) != 0) {
        qrzl_log("switch_sim_card_lock init failed\n");
    }
    if (pthread_mutex_init(&update_device_dynamic_data_lock, NULL) != 0) {
        qrzl_log("update_device_dynamic_data_lock init failed\n");
    }
}

void qrzl_utils_destroy()
{
    pthread_mutex_destroy(&switch_sim_card_lock);
    pthread_mutex_destroy(&update_device_dynamic_data_lock);
}

int nv_update_net_band()
{
    int ret = 0;
	int act = 0;
	int band = 0;
	void *p[] = {&act, &band};
	ret = get_modem_info2("AT+ZBAND?\r", "%d,%d", (void**)p,0,10);
	if (ret != 0) 
	{
		qrzl_err("get modem info err: %d\n", ret);
        return ret;
	}
	else 
	{
		qrzl_log("act = %d, band = %d\n", act, band);
		if (act == 3 && band != 0 && band != 255) 
		{
			char tmpstr[10] = {0};
			sprintf(tmpstr, "B %d", band);
			cfg_set(NV_CURRENT_NET_BAND, tmpstr);
		}
		else
		{
			cfg_set(NV_CURRENT_NET_BAND, "--");
		}
	}

    int pa_adc = 0;
    void *PATEMP_p[] = {&pa_adc};
    ret = get_modem_info2("AT+GETPATEMP?\r", "%d", (void**)PATEMP_p,0,10);
    if (ret == 0) {
        qrzl_log("pa_adc: %d", pa_adc);
    }

    return 0;
}

int nv_update_net_cesq_info()
{
    int ret = 0;
	int rxlev, ber, rscp, ecno, rsrq, rsrp; 
	void *p[] = {&rxlev, &ber, &rscp, &ecno, &rsrq, &rsrp};
	ret = get_modem_info2("AT+CESQ\r", "%d,%d,%d,%d,%d,%d", (void**)p,0,10);
	if (ret != 0) 
	{
		qrzl_err("get cesq err: %d\n", ret);
        return ret;
	}
	else 
	{
        char rsrq_str[10] = {0};
        snprintf(rsrq_str, sizeof(rsrq_str), "%d", rsrq);
        cfg_set("rsrq", rsrq_str);
	}

    return 0;
}

int nv_update_sn()
{
    int ret = 0;
    char sn[21] = {0};
    void *p[] = {sn};
    ret = get_modem_info2("AT+BOARDNUM?\r", "%s",  p,0,5);
    if (ret != 0) 
	{
		qrzl_err("get sn err: %d\n", ret);
        return ret;
	}
    if (ret == 0 && strlen(sn) >= 12) {
        qrzl_log("sn = %s", sn);
        if (is_alphanumeric(sn) != 0)
        {
            return -1;
        }
        snprintf(g_qrzl_device_static_data.sn, sizeof(g_qrzl_device_static_data.sn), "%s", sn);
        cfg_set(NV_SN, sn);
    }
    return 0;
}

void get_lte_band(char *band)
{
    int ret;
    int b1, b2, b3, b4, b5, b6, b7, b8 = 0;
    void *p[] = {&b1, &b2, &b3, &b4, &b5, &b6, &b7, &b8};
    ret = get_modem_info2("AT+ZLTEBAND?\r", "%d,%d,%d,%d,%d,%d,%d,%d", (void**)p,0,10);
	if (ret != 0) 
	{
		qrzl_err("get ZLTEBAND info err: %d\n", ret);
	}
    else
    {
        sprintf(band, "%d,%d,%d,%d,%d,%d,%d,%d", b1, b2, b3, b4, b5, b6, b7, b8);
    }
}

/* 这个函数已在qrzl_device_control.c中定义，这里不重复定义 */

/**
 * 更新电量百分比的值
 */
static int update_battery_vol_percent()
{
    int ret;
    FILE *fp;
    int voltage_now;
    int percentage = 0;
    char voltage_str[128] = {0};
    ret = cfg_get_item("mmi_battery_voltage_line", voltage_str, sizeof(voltage_str));
    if (ret != 0)
    {
        qrzl_err("Failed to get mmi_battery_voltage_line");
        return -1;
    }

    // 读取电压值
    fp = fopen("/sys/class/power_supply/battery/voltage_now", "r");
    if (!fp) {
        qrzl_err("Failed to open voltage file");
        cfg_set("alk_battery_percent", "100");
        cfg_set("battery_vol_percent", "100");
        return -1;
    }
    if (fscanf(fp, "%d", &voltage_now) != 1) {
        qrzl_err("Failed to read voltage value");
        cfg_set("alk_battery_percent", "100");
        cfg_set("battery_vol_percent", "100");
        fclose(fp);
        return -1;
    }
    fclose(fp);

    // 分割电压字符串并比较
    char *token;
    char voltage_copy[256];
    strncpy(voltage_copy, voltage_str, sizeof(voltage_copy) - 1);
    voltage_copy[sizeof(voltage_copy) - 1] = '\0';
    token = strtok(voltage_copy, "+");
    int current_voltage;
    int step = 5;  // 每个区间的百分比增量

    while (token) {
        current_voltage = atoi(token);
        if (voltage_now < current_voltage) {
            break;
        }
        percentage += step;
        token = strtok(NULL, "+");
    }
    percentage -= 5;
    if (percentage < 0) {
        percentage = 0;
    }
    char percentage_str[4] = {0};
    snprintf(percentage_str, sizeof(percentage_str), "%d", percentage);
    cfg_set("alk_battery_percent", percentage_str);
    return cfg_set("battery_vol_percent", percentage_str);
}

/**
 * 判断一个字符串是否在另一个字符串中
 * 例如：ESIM1_only,ESIM2_only,RSIM_only， 判断 ESIM1_only 是否存在
 * 找到返回 1，未找到返回 0
 */
int contains_type(const char *input, const char *target) {
    char temp[128];
    strncpy(temp, input, sizeof(temp));
    temp[sizeof(temp) - 1] = '\0';  // 保证末尾有 '\0'

    char *token = strtok(temp, ",");
    while (token != NULL) {
        if (strcmp(token, target) == 0) {
            return 1; // 找到了
        }
        token = strtok(NULL, ",");
    }
    return 0; // 没找到
}

/**
 * 移除字符串中指定项（保留逗号分隔格式）
 */
void remove_item_from_csv(char *input, const char *target) {
    char result[256] = {0};
    char *token = strtok(input, ",");
    int first = 1;

    while (token != NULL) {
        if (strcmp(token, target) != 0) {
            if (!first) {
                strcat(result, ",");
            }
            strcat(result, token);
            first = 0;
        }
        token = strtok(NULL, ",");
    }

    // 将结果写回原始 input
    strcpy(input, result);
}

/**
 * 向 CSV 字符串中添加子字符串（防止重复）
 */
void add_item_to_csv(char *csv, const char *item) {
    if (!contains_type(csv, item)) {
        if (strlen(csv) > 0) {
            strcat(csv, ",");
        }
        strcat(csv, item);
    }
}

/**
 * 更新主板温度
 */
static int update_board_temperature()
{
    int ret;
    char pa_temperature_str[1024] = {0};
    int percentage = 90;

    ret = cfg_get_item("board_pa_temperature_line", pa_temperature_str, sizeof(pa_temperature_str));
    if (ret != 0)
    {
        qrzl_err("Failed to get board_pa_temperature_line");
        return -1;
    }

    // 发送at获取当前pa温度
    int pa_adc = 0;
    void *PATEMP_p[] = {&pa_adc};
    ret = get_modem_info2("AT+GETPATEMP?\r", "%d", (void**)PATEMP_p,0,10);
    if (ret == 0) {
        qrzl_log("pa_adc: %d", pa_adc);
    }

    // 分割温度字符串并比较
    char *token;
    char pa_temperature_copy[256];
    strncpy(pa_temperature_copy, pa_temperature_str, sizeof(pa_temperature_copy) - 1);
    pa_temperature_copy[sizeof(pa_temperature_copy) - 1] = '\0';
    token = strtok(pa_temperature_copy, "+");
    int current_temperature;
    int step = 5;  // 每个区间的百分比增量

    while (token) {
        current_temperature = atoi(token);
        if (pa_adc < current_temperature) {
            break;
        }
        percentage -= step;
        token = strtok(NULL, "+");
    }
    char percentage_str[4] = {0};
    snprintf(percentage_str, sizeof(percentage_str), "%d", percentage);
    return cfg_set("board_temperature", percentage_str);
}

/**
 * 获取当前SIM卡的卡槽号
 * 0: sim, 1: esim1, 2: esim2
 * -1: 未知
 */
int get_device_current_sim_index()
{
    int ret;
    int card_switch_type = -1;
    void *p[] = {&card_switch_type};
    ret = get_modem_info2("AT+ZCARDSWITCH?\r", "%d", (void**)p, 0, 10);
	if (ret != 0) 
	{
		qrzl_err("获取当前卡通道失败 err: %d\n", ret);
        return -1;
	}

    /**
     * DZ802的卡通道和之前的是相反的，这里就做一下简单处理
     */
#if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5) || defined(JCV_HW_MZ806_V1) || defined(JCV_HW_GS28V_V1) || defined(JCV_HW_DZ803_V1_0)
    if (card_switch_type == 0) {
        card_switch_type = 3;
    } else if(card_switch_type == 3) {
        card_switch_type = 0;
    }
#endif

/**
 * 请注意，qrzl_app程序中的current_sim代表是逻辑上的RSIM ESIM1 ESIM2，NV中的sim_select表示的是板子上实际的物理通道
 */
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (card_switch_type == 0) {
        return 2;
    } else if (card_switch_type == 3) {
        return 1;
    }
    return -1;
#else
    if (card_switch_type == 0) {
        return 0;
    } else if (card_switch_type == 3) {

        FILE *sim_siwtch_fp;
        int esim_num = -1;

        // 打开 sysfs 文件
        sim_siwtch_fp = fopen("/sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness", "r");
        if (sim_siwtch_fp != NULL) {
            // 读取数值
            if (fscanf(sim_siwtch_fp, "%d", &esim_num) != 1) {
                qrzl_err("Failed to read /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness value");
            }
            // 关闭文件
            fclose(sim_siwtch_fp);
        } else {
            qrzl_err("Failed to open /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        if (esim_num == 0) {
            return 1;
        } else if (esim_num == 1) {
            return 2;
        }
        return -1;
    }
#endif
}

/* 这个函数已在qrzl_device_control.c中定义，这里不重复定义 */



EsimFluxStat get_esim_fluxstat()
{
    EsimFluxStat esim_flux_stat;
    memset(&esim_flux_stat, 0, sizeof(EsimFluxStat));
    
    char rsim_flux_total[128] = {0};
    char rsim_flux_day_total[128] = {0};
    char rsim_flux_month_total[128] = {0};

	char esim1_flux_total[128] = {0};
    char esim1_flux_day_total[128] = {0};
    char esim1_flux_month_total[128] = {0};

	char esim2_flux_total[128] = {0};
	char esim2_flux_day_total[128] = {0};
	char esim2_flux_month_total[128] = {0};

    cfg_get_item("rsim_flux_total", rsim_flux_total, 128);
    cfg_get_item("rsim_flux_day_total", rsim_flux_day_total, 128);
    cfg_get_item("rsim_flux_month_total", rsim_flux_month_total, 128);

    cfg_get_item("esim1_flux_total", esim1_flux_total, 128);
    cfg_get_item("esim1_flux_day_total", esim1_flux_day_total, 128);
    cfg_get_item("esim1_flux_month_total", esim1_flux_month_total, 128);

    cfg_get_item("esim2_flux_total", esim2_flux_total, 128);
    cfg_get_item("esim2_flux_day_total", esim2_flux_day_total, 128);
    cfg_get_item("esim2_flux_month_total", esim2_flux_month_total, 128);

    esim_flux_stat.esim1_flux_total = atoll(esim1_flux_total);
    esim_flux_stat.esim1_flux_day_total = atoll(esim1_flux_day_total);
    esim_flux_stat.esim1_flux_month_total = atoll(esim1_flux_month_total);
    
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    
    esim_flux_stat.esim2_flux_total = atoll(rsim_flux_total);
    esim_flux_stat.esim2_flux_day_total = atoll(rsim_flux_day_total);
    esim_flux_stat.esim2_flux_month_total = atoll(rsim_flux_month_total);
#else
    esim_flux_stat.rsim_flux_total = atoll(rsim_flux_total);
    esim_flux_stat.rsim_flux_day_total = atoll(rsim_flux_day_total);
    esim_flux_stat.rsim_flux_month_total = atoll(rsim_flux_month_total);

    esim_flux_stat.esim2_flux_total = atoll(esim2_flux_total);
    esim_flux_stat.esim2_flux_day_total = atoll(esim2_flux_day_total);
    esim_flux_stat.esim2_flux_month_total = atoll(esim2_flux_month_total);
#endif
    

    



    qrzl_log("rsim_flux_total: %lld, esim1_flux_total: %lld, esim2_flux_total: %lld, rsim_flux_day_total: %lld, esim1_flux_day_total: %lld, esim2_flux_day_total: %lld, rsim_flux_month_total: %lld, esim1_flux_month_total: %lld, esim2_flux_month_total: %lld",
             esim_flux_stat.rsim_flux_total, esim_flux_stat.esim1_flux_total, esim_flux_stat.esim2_flux_total,
             esim_flux_stat.rsim_flux_day_total, esim_flux_stat.esim1_flux_day_total, esim_flux_stat.esim2_flux_day_total,
             esim_flux_stat.rsim_flux_month_total, esim_flux_stat.esim1_flux_month_total, esim_flux_stat.esim2_flux_month_total);
    return esim_flux_stat;
}

/**
 * 获取当前SIM卡的卡槽号，是通过全局变量 g_qrzl_device_dynamic_data.current_sim 中的值来直接判断，不具备实时性，谨慎使用
 * 0: sim, 1: esim1, 2: esim2
 * -1: 未知
 */
int get_device_current_sim_index_by_data()
{
    if (strcmp("ESIM1_only", g_qrzl_device_dynamic_data.current_sim) == 0) {
        return 1;
    } 
    else if (strcmp("ESIM2_only", g_qrzl_device_dynamic_data.current_sim) == 0) {
        return 2;
    }
    else if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0) {
        return 0;
    }
    return -1;
}

/**
 * 限制上下行网速, 单位Kbps
 */
int limit_net_speed(uint64_t up_limit, uint64_t down_limit)
{
#ifdef QRZL_DEVICE_CONTROL_ENABLE
    char qrzl_limit_down_speed[21] = {0};
    char qrzl_limit_up_speed[21] = {0};
    cfg_get_item("qrzl_limit_down_speed", qrzl_limit_down_speed, sizeof(qrzl_limit_down_speed));
    cfg_get_item("qrzl_limit_up_speed", qrzl_limit_up_speed, sizeof(qrzl_limit_up_speed));
    uint64_t limit_down_speed_num = strtoull(qrzl_limit_down_speed, NULL, 10);
    uint64_t limit_up_speed_num = strtoull(qrzl_limit_up_speed, NULL, 10);
    if (limit_down_speed_num > 0 || limit_up_speed_num > 0) {
        up_limit = limit_up_speed_num;
        down_limit = limit_down_speed_num;
    }
#endif
    /* up_limit和down_limit是unsigned类型，不需要检查小于0 */
    (void)up_limit; (void)down_limit; /* 避免未使用变量警告 */
    {
        qrzl_err("limit_net_speed vaule error");
        return -1;
    }
    // 利用/sbin/tc_tbf.sh脚本限速，nv的单位是byte 
    uint64_t tc_uplink_byte = 0L;
    uint64_t tc_downlink_byte = 0L;
    cfg_set("tc_enable", "1");
    tc_uplink_byte = (up_limit * 1000) / 8; 
    tc_downlink_byte = (down_limit * 1000) / 8;
    
    char tc_uplink[21] = {0};
    char tc_downlink[21] = {0};
    snprintf(tc_uplink, sizeof(tc_uplink), "%lld", tc_uplink_byte);
    snprintf(tc_downlink, sizeof(tc_downlink), "%lld", tc_downlink_byte);
    cfg_set("tc_uplink", tc_uplink);
    cfg_set("tc_downlink", tc_downlink);
    system("/sbin/tc_tbf.sh");
    return 0;
}

/**
 * 获取上行限速值，单位Kbps
 */
uint64_t get_up_limit_net_speed()
{
    int ret;
    uint64_t up_limit = 0L;
    char up_limit_str[21] = {0};
    ret = cfg_get_item("tc_uplink", up_limit_str, sizeof(up_limit_str));
    if (ret != 0)
    {
        return up_limit;
    }
    up_limit = atoll(up_limit_str);
    return (up_limit * 8) / 1000;
}

/**
 * 获取下行限速值，单位Kbps
 */
uint64_t get_down_limit_net_speed()
{
    int ret;
    uint64_t down_limit = 0L;
    char down_limit_str[21] = {0};
    ret = cfg_get_item("tc_downlink", down_limit_str, sizeof(down_limit_str));
    if (ret != 0)
    {
        return down_limit;
    }
    down_limit = atoll(down_limit_str);
    return (down_limit * 8) / 1000;
}

int init_wifi_config_value(struct wifi_config_t *wifi_config)
{
    wifi_config->max_access_num = g_qrzl_device_dynamic_data.max_access_num;
    wifi_config->enable = g_qrzl_device_dynamic_data.wifi_enable;
    wifi_config->hide = g_qrzl_device_dynamic_data.wifi_hide;
    strlcpy(wifi_config->ssid, g_qrzl_device_dynamic_data.wifi_ssid, sizeof(wifi_config->ssid));
    strlcpy(wifi_config->key, g_qrzl_device_dynamic_data.wifi_key, sizeof(wifi_config->key));
    strlcpy(wifi_config->auth_mode, g_qrzl_device_dynamic_data.wifi_auth_mode, sizeof(wifi_config->auth_mode));
    return 0;
}

int update_wifi_by_config(const struct wifi_config_t *wifi_config)
{   
    qrzl_log("start update_wifi_by_config");
    unsigned int wifi_set_flags = 0;
    char wifi_set_flags_str[8] = {0};
    if (wifi_config->enable != g_qrzl_device_dynamic_data.wifi_enable)
    {
        char wifi_enable_c[2] = {0};
        snprintf(wifi_enable_c, sizeof(wifi_enable_c), "%d", wifi_config->enable);
        cfg_set("wifiEnabled", wifi_enable_c);
        char flag_str[8]={0}; 
        if (wifi_config->enable)
        {
            qrzl_log("enable wifi");
            snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_OPEN); 
        }
        else
        {
            qrzl_log("disable WiFi");
            snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_CLOSE); 
            ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, (unsigned char *)flag_str, 0);
            return 0;
        }
        
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, (unsigned char *)flag_str, 0);
    }

    if (strlen(wifi_config->ssid) > 0 && strcmp(wifi_config->ssid, g_qrzl_device_dynamic_data.wifi_ssid) != 0) 
    {
        qrzl_log("update wifi ssid");
        wifi_set_flags |= ZTE_WLAN_SSID_SET;
        cfg_set("SSID1", (char *)wifi_config->ssid);
        wifi_set_flags |= ZTE_WLAN_BASIC_SECURITY_SET; // ssid更新，WiFi密码也一定要更新
    }

    if (strcmp(wifi_config->auth_mode, g_qrzl_device_dynamic_data.wifi_auth_mode) != 0)
    {
        qrzl_log("update wifi auth");
        if (strlen(wifi_config->key) == 0 && strcmp(wifi_config->auth_mode, "OPEN") != 0)
        {
            qrzl_log("在WiFi非开放模式下，密码不能为空");
        }
        else
        {
            wifi_set_flags |= ZTE_WLAN_BASIC_SECURITY_SET;
            if (strcmp(wifi_config->auth_mode, "OPEN") == 0)
            {
                cfg_set("AuthMode", "OPEN");
                cfg_set("EncrypType", "NONE");
                cfg_set("WPAPSK1", "");
                cfg_set("WPAPSK1_encode", "");
                cfg_set("cipher_str", "2");
            }
            else if (strcmp(wifi_config->auth_mode, "WPA2PSK") == 0)
            {
                cfg_set("AuthMode", "WPA2PSK");
                cfg_set("EncrypType", "AES");
                cfg_set("cipher_str", "1");
            }
            else if (strcmp(wifi_config->auth_mode, "WPAPSKWPA2PSK") == 0)
            {
                cfg_set("AuthMode", "WPAPSKWPA2PSK");
                cfg_set("EncrypType", "TKIPAES");
                cfg_set("cipher_str", "2");
            }
            else if (strcmp(wifi_config->auth_mode, "WPA3Personal") == 0)
            {
                cfg_set("AuthMode", "WPA3Personal");
                cfg_set("EncrypType", "AES");
                cfg_set("cipher_str", "1");
            }
            else if (strcmp(wifi_config->auth_mode, "WPA2WPA3") == 0)
            {
                cfg_set("AuthMode", "WPA2WPA3");
                cfg_set("EncrypType", "AES");
                cfg_set("cipher_str", "1");
            }
        }
    }

    if (strlen(wifi_config->key) > 7 && strcmp(wifi_config->key, g_qrzl_device_dynamic_data.wifi_key) != 0) 
    {
        qrzl_log("update wifi key");
        qrzl_log("wifi_config->key: %s", wifi_config->key);
        char *key_encode_str = NULL;

        key_encode_str = qrzl_base64_encode(wifi_config->key, strlen(wifi_config->key));
        if (NULL == key_encode_str)
        {
            qrzl_err("key_encode is NULL");
            free(key_encode_str);
        } 
        else
        {
            qrzl_log("encode key: %s", key_encode_str);
            cfg_set("WPAPSK1_encode", key_encode_str);
            free(key_encode_str);

            wifi_set_flags |= ZTE_WLAN_BASIC_SECURITY_SET;
            cfg_set("WPAPSK1", (char *)wifi_config->key);
        }
    }

    if (wifi_config->max_access_num > 0 && wifi_config->max_access_num != g_qrzl_device_dynamic_data.max_access_num)
    {
        qrzl_log("update wifi max access");
        char max_access_num[3] = {0};
        snprintf(max_access_num, sizeof(max_access_num), "%d", wifi_config->max_access_num);
        cfg_set("MAX_Access_num", max_access_num);
        cfg_set("MAX_Access_num_user_set", "1");
        wifi_set_flags |= ZTE_WLAN_MAX_ACCESS_NUM_SET;
    }
    if (wifi_config->hide != g_qrzl_device_dynamic_data.wifi_hide && wifi_config->hide >= 0 && wifi_config->hide <= 1)
    {
        qrzl_log("update wifi is hide");
        char wifi_hide_c[2] = {0};
        snprintf(wifi_hide_c, sizeof(wifi_hide_c), "%d", wifi_config->hide);
        cfg_set("HideSSID", wifi_hide_c);
        wifi_set_flags |= ZTE_WLAN_BROADCAST_SET;
    }

    if (wifi_set_flags != 0) {
        qrzl_log("WiFi参数有修改，开始更新WiFi");
        snprintf(wifi_set_flags_str, sizeof(wifi_set_flags_str) - 1, "%u", wifi_set_flags); 
        cfg_set("wifi_set_flags", wifi_set_flags_str);
 
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_CFG_AP, 0, NULL, 0);
    #ifdef JCV_HW_MZ801_V1_2
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_MMI, MSG_CMD_MODIFY_SSID_KEY, 0, NULL, 0);
    #endif
    }
    return 0;
}

int wifi_switch(int status)
{
    int ret;
    char wifi_enabled[2];
    ret = cfg_get_item("wifiEnabled", wifi_enabled, 2);
    (void)ret; /* 避免未使用变量警告 */
    int wifi_enabled_num = atoi(wifi_enabled);
    if (status == wifi_enabled_num)
    {
        qrzl_log("WiFi状态已经是目标状态");
        return 1;
    }
    char flag_str[8]={0}; 
    if (status == 1)
    {
        qrzl_log("开启wifi");
        cfg_set("wifiEnabled", "1");
        snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_OPEN); 
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, (unsigned char *)flag_str, 0);
    }
    else
    {
        qrzl_log("关闭WiFi");
        cfg_set("wifiEnabled", "0");
        snprintf(flag_str, sizeof(flag_str), "%u", WIFI_ADVANCED_CLOSE); 
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_ADVANCED, strlen(flag_str) + 1, (unsigned char *)flag_str, 0);
        
    }
    return 0;
}

int restart_device()
{
    cfg_save();
    return restart_request(MODULE_ID_QRZL_APP);
}

int reset_device()
{
    return reset_request(MODULE_ID_QRZL_APP);
}

int shutdown_device()
{
    cfg_save();
    return poweroff_request(MODULE_ID_QRZL_APP);
}

int update_web_password(const char *password)
{
    if (password != NULL && strlen(password) > 3)
    {
        return cfg_set("admin_Password", (char *)password);
    }
    return 0;
}

int init_mac_filter_config_value(struct mac_filter_config_t *mac_filter_config)
{
    mac_filter_config->wifi_filter_type = g_qrzl_device_dynamic_data.wifi_filter_type;
    strlcpy(mac_filter_config->mac_black_list, g_qrzl_device_dynamic_data.mac_black_list, sizeof(mac_filter_config->mac_black_list));
    strlcpy(mac_filter_config->mac_white_list, g_qrzl_device_dynamic_data.mac_white_list, sizeof(mac_filter_config->mac_white_list));
    return 0;
}

int update_mac_filter_by_config(const struct mac_filter_config_t *mac_filter_config)
{
    qrzl_log("start update_mac_filter_by_config");
    unsigned int wifi_set_flags = 0;
	char wifi_set_flags_str[20] = {0};
    char acl_mode[2] = {0};
    if (mac_filter_config->wifi_filter_type != g_qrzl_device_dynamic_data.wifi_filter_type)
    {
        if (mac_filter_config->wifi_filter_type >= 0 && mac_filter_config->wifi_filter_type <= 2)
        {
            snprintf(acl_mode, sizeof(acl_mode), "%d", mac_filter_config->wifi_filter_type);
            cfg_set("ACL_mode", acl_mode);
            wifi_set_flags |= ZTE_WLAN_ACL_SET;
        }
    }
    if (strcmp(mac_filter_config->mac_black_list, g_qrzl_device_dynamic_data.mac_black_list) != 0)
    {
        cfg_set("wifi_mac_black_list", (char *)mac_filter_config->mac_black_list);
        wifi_set_flags |= ZTE_WLAN_ACL_SET;
    }
    if (strcmp(mac_filter_config->mac_white_list, g_qrzl_device_dynamic_data.mac_white_list) != 0)
    {
        cfg_set("wifi_mac_white_list", (char *)mac_filter_config->mac_white_list);
        wifi_set_flags |= ZTE_WLAN_ACL_SET;
    }
    if (wifi_set_flags != 0)
    {
        snprintf(wifi_set_flags_str, sizeof(wifi_set_flags_str) - 1, "%u", wifi_set_flags);
        cfg_set("wifi_set_flags", wifi_set_flags_str);
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_WIFI, MSG_CMD_WIFI_MAC, 0, NULL, 0);
    }
    return 0;
}

int set_lte_net_band(int band)
{
	int band_bitsp[8] = {0};
    qrzl_log("准备设置BAND = %d\n", band);
	char at_str_tmp[128] = {0};
	if (band <= 0)
	{
		// 自动
		sprintf(at_str_tmp, "AT+ZLTEBAND=\r");
	} 
	else 
	{
		int jump_num = (band - 1) / 8; // 由于设置band是按bit位设置，并且是每8位分割，这个变量是表面要跳过多少个分割
		int bit_num = (band - 1) % 8; // 这个是每8位后，bit位最后要设置的band
		band_bitsp[jump_num] = 1;
		band_bitsp[jump_num] = band_bitsp[jump_num] << bit_num;
		sprintf(at_str_tmp, "AT+ZLTEBAND=%d,%d,%d,%d,%d,%d,%d,%d\r\n",
			band_bitsp[0], band_bitsp[1], band_bitsp[2], band_bitsp[3],
			band_bitsp[4], band_bitsp[5], band_bitsp[6], band_bitsp[7]);
		qrzl_log("at_str_tmp = %s\n", at_str_tmp);
	}
	int ret = get_modem_info2(at_str_tmp, NULL, NULL, 0, 10);
	if (ret != 0) {
		qrzl_err("set net band err\n");
		return -1;
	}
	char cfg_str_tmp[128] = {0};
	sprintf(cfg_str_tmp, "%d", band);
	cfg_set(NV_SELECTED_NET_BAND, cfg_str_tmp);
	cfg_save();
    return 0;
}

/**
 * 这个函数的切卡sim_type代表实际物理通道对应的卡，与逻辑切卡可能存在不一样，对接客户的时候时候尽量不要使用这个函数，
 * 而是使用switch_sim_card 或者 switch_sim_card_not_restart
 * sim_type: 0 外卡，1 esim1， 2esim2
 * 
 * 0 切卡成功
 * 2 切卡失败
 * -1 AT失败，NV设置为目标值后，重启设备
 * 
 * 这个方法的代码可能会看着有点奇怪，因为在执行切卡操作时会涉及到AT命令的执行，而AT命令的执行有可能会失败
 * 所以如果AT命令失败，会重试3次，如果3次都是失败，那么会将NV设置为目标值，然后重启设备
 */
int switch_sim_card_core(int sim_type)
{
    qrzl_log("开始切卡至物理通道卡: %d", sim_type);
    pthread_mutex_lock(&switch_sim_card_lock);  // 加锁，进入临界区
    int ret;
    char now_nv_sim_select[16] = {0};
    ret = cfg_get_item("sim_select", now_nv_sim_select, sizeof(now_nv_sim_select));
    if (ret != 0)
    {
        qrzl_err("无法获取 sim_select value");
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 2;
    }

    // 中芯微卡有两个通道，分别对应实体卡通道和ESIM卡通道,在当前设备对应的版本 0 是实体卡, 3是ESIM卡
    int card_switch_type = 0;
    void *p[] = {&card_switch_type};
    ret = get_modem_info2("AT+ZCARDSWITCH?\r", "%d", (void**)p, 0, 10);
	if (ret != 0) 
	{
		qrzl_err("获取当前卡通道失败 err: %d\n", ret);
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return -1;
	}
    qrzl_log("当前sim卡通道值: %d", card_switch_type);

    // DZ802 卡通道 0 是esim卡，3是sim卡
#if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5) || defined(JCV_HW_MZ806_V1) || defined(JCV_HW_GS28V_V1) || defined(JCV_HW_DZ803_V1_0)
    int i;
    if (sim_type == 0) {
        if (card_switch_type != 3) {
            qrzl_log("准备切换到实卡");

            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+ZCARDSWITCH=3\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", "RSIM_only");
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;
    }
    else if (sim_type == 1 || sim_type == 2)
    {
        char esim_nv_value[64] = {0};
        if (sim_type == 1)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM1_only");
        }
        else if (sim_type == 2)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM2_only");
        }
        qrzl_log("要设置的ESIM卡为: %s", esim_nv_value);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        qrzl_log("准备切换到ESIM卡通道");
        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+ZCARDSWITCH=0\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        if (sim_type == 1) {
            qrzl_log("准备切到ESIM1");
            system("/bin/echo 0 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        else if (sim_type == 2) {
            qrzl_log("准备切到ESIM2");
            system("/bin/echo 1 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", esim_nv_value);
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;    
    }
#else
    int i;
    if (sim_type == 0) {
        if (card_switch_type != 0) {
            qrzl_log("准备切换到实卡");

            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+ZCARDSWITCH=0\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", "RSIM_only");
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", "RSIM_only");
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;
    }
    else if (sim_type == 1 || sim_type == 2)
    {
        char esim_nv_value[64] = {0};
        if (sim_type == 1)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM1_only");
        }
        else if (sim_type == 2)
        {
            snprintf(esim_nv_value, sizeof(esim_nv_value), "%s", "ESIM2_only");
        }
        qrzl_log("要设置的ESIM卡为: %s", esim_nv_value);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=5\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);


        if (card_switch_type != 3) {
            qrzl_log("准备切换到ESIM卡通道");
            for (i =0; i<3; i++) {
                if (get_modem_info2("AT+ZCARDSWITCH=3\r", NULL, NULL, 0, 10) != 0) {
                    if (i == 2) {
                        cfg_set("prev_sim_select", now_nv_sim_select);
                        cfg_set("sim_select", esim_nv_value);
                        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                        restart_device();
                        return -1;
                    }
                    sleep(1);
                } else {
                    break;
                }
            }
            sleep(1);
        }

        if (sim_type == 1) {
            qrzl_log("准备切到ESIM1");
            system("/bin/echo 0 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        else if (sim_type == 2) {
            qrzl_log("准备切到ESIM2");
            system("/bin/echo 1 > /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }
        sleep(1);

        for (i =0; i<3; i++) {
            if (get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10) != 0) {
                if (i == 2) {
                    cfg_set("prev_sim_select", now_nv_sim_select);
                    cfg_set("sim_select", esim_nv_value);
                    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
                    restart_device();
                    return -1;
                }
                sleep(1);
            } else {
                break;
            }
        }

        cfg_set("prev_sim_select", now_nv_sim_select);
        cfg_set("sim_select", esim_nv_value);
        pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
        return 0;    
    }
#endif

    pthread_mutex_unlock(&switch_sim_card_lock);  // 解锁，离开临界区
    return 2;
}

/**
 * 切卡
 * sim: 0
 * esim1: 1
 * esim2: 2
 */
int switch_sim_card(int sim_type)
{
    int current_sim_index = get_device_current_sim_index();
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (sim_type == 2) {
        sim_type = 0;
    }
    if (current_sim_index == 2) {
        current_sim_index = 0;
    }
#endif

    if (current_sim_index != sim_type) {
        if (switch_sim_card_core(sim_type) == 0)
        {
            qrzl_log("切卡成功,准备重启");
            restart_device();
        }
    } else {
        qrzl_log("已是目标卡，无需切卡");
        char now_nv_sim_select[16] = {0};
        cfg_get_item("sim_select", now_nv_sim_select, sizeof(now_nv_sim_select));
        if (current_sim_index == 0 && strcmp(RSIM_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", RSIM_ONLY_STR);
        } else if (current_sim_index == 1 && strcmp(ESIM1_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM1_ONLY_STR);
        } else if (current_sim_index == 2 && strcmp(ESIM2_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM2_ONLY_STR);
        }
    }
    
    return 0;
}

int get_current_isp()
{
    // 获取运营商
    char sim_imsi[32] = {0};
    cfg_get_item("sim_imsi", sim_imsi, sizeof(sim_imsi));
    return get_isp_by_imsi(sim_imsi);
}

/**
 * 根据当前运营商重新更新本地认证信息
 */
void re_update_local_auth_info()
{
    // // 重新更新本地认证信息
    // int isp_res = get_current_isp();
    // // 0没有匹配的运营商, 1 china_mobile, 2 china_united, 3 china_telecom
    // qrzl_log("re_update_local_auth_info: %d", isp_res);
    // switch (isp_res)
    // {
    // case 1:
    //     init_onelink_authed_info();
    //     break;
    // case 2:
    //     break;
    // case 3:
    //     init_cmp_authed_info();
    //     break;
    // default:
    //     break;
    // }
#ifdef QRZL_AUTH_ONE_LINK_HTTP
    init_onelink_authed_info();
#endif 

#ifdef QRZL_AUTH_CMP_HTTP
    init_cmp_authed_info();
#endif 
}

/**
 * 切卡
 * sim: 0
 * esim1: 1
 * esim2: 2
 */
int switch_sim_card_not_restart(int sim_type)
{
    int current_sim_index = get_device_current_sim_index();
#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (sim_type == 2) {
        sim_type = 0;
    }
    if (current_sim_index == 2) {
        current_sim_index = 0;
    }
#endif

    qrzl_log("current_sim_index: %d, sim_type: %d", current_sim_index, sim_type);
    if (current_sim_index != sim_type) {

        int switch_res = switch_sim_card_core(sim_type);
        if (switch_res == 0) {
            qrzl_log("切卡完成!");
#if defined(JCV_FEATURE_CAPTIVE_PORTAL_SERVER) || defined(JCV_FEATURE_WIFI_RECONNECT_PORTAL) || defined(ZXIC_ONELINK_TEST)
            // * 在认证中，触发非重启切卡，需清理已认证的MAC，避免非认证卡的MAC混入认证卡中
            system("echo clear > /proc/cjportal/auth");
            qrzl_log("Certification information has been cleared.");
            re_update_local_auth_info();
#elif defined(QRZL_WIFIDOG_ONELINK)
            // wifidog 模式下只需要更新认证信息
            re_update_local_auth_info();
#endif
        }

        return switch_res;
    } else {
        qrzl_log("已是目标卡，无需切卡");
        char now_nv_sim_select[16] = {0};
        cfg_get_item("sim_select", now_nv_sim_select, sizeof(now_nv_sim_select));
        if (current_sim_index == 0 && strcmp(RSIM_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", RSIM_ONLY_STR);
        } else if (current_sim_index == 1 && strcmp(ESIM1_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM1_ONLY_STR);
        } else if (current_sim_index == 2 && strcmp(ESIM2_ONLY_STR, now_nv_sim_select) != 0) {
            cfg_set("prev_sim_select", now_nv_sim_select);
            cfg_set("sim_select", ESIM2_ONLY_STR);
        }
    }
    return 0;
}

int get_csq()
{
    int rssi = atoi(g_qrzl_device_dynamic_data.rssi);
    if (rssi == 0)
    {
        return 99;
    }
    int csq;
    if(rssi < -113)
        csq = 0;
    else if(rssi == -111)
        csq = 1;
    else if((rssi>=-109)&&(rssi<=-53))
    {
        csq = (rssi + 113)/2;
    }
    else if((rssi>= -51)&&(rssi<0))
    {
        csq = 31;
    }
    else
        csq = 99;
    return csq;
}

// 0没有匹配的运营商, 1 china_mobile, 2 china_united, 3 china_telecom
int get_isp_by_imsi(const char *imsi)
{
    // 检查IMSI是否为空或长度是否不足
    if (imsi == NULL || strlen(imsi) < 5) {
        return 0;
    }

    // 根据IMSI的前缀判断运营商
    if (strncmp(imsi, "46000", 5) == 0 || strncmp(imsi, "46002", 5) == 0 || strncmp(imsi, "46004", 5) == 0 || strncmp(imsi, "46007", 5) == 0
        || strncmp(imsi, "46008", 5) == 0 || strncmp(imsi, "46013", 5) == 0 || strncmp(imsi, "46024", 5) == 0) {
        return 1; // 中国移动
    } else if (strncmp(imsi, "46001", 5) == 0 || strncmp(imsi, "46006", 5) == 0 || strncmp(imsi, "46009", 5) == 0 || strncmp(imsi, "46010", 5) == 0) {
        return 2; // 中国联通
    } else if (strncmp(imsi, "46003", 5) == 0 || strncmp(imsi, "46005", 5) == 0 || strncmp(imsi, "46011", 5) == 0 || strncmp(imsi, "46012", 5) == 0) {
        return 3; // 中国电信
    } else {
        return 0; // 未匹配
    }
}

int get_isp_name_cn_by_imsi(const char *imsi, char *mno, size_t mno_size)
{
    int isp = get_isp_by_imsi(imsi);
    if (isp == 1)
    {
        snprintf(mno, mno_size, "%s", "中国移动");
    }
    else if (isp == 2)
    {
        snprintf(mno, mno_size, "%s", "中国联通");
    }
    else if (isp == 3)
    {
        snprintf(mno, mno_size, "%s", "中国电信");
    }
    else 
    {
        snprintf(mno, mno_size, "%s", "未知");
    }
    return 0;
}

int nv_set_esim_mno(int esim_num, const char *imsi)
{
    char mno[20] = {0};
    int isp = get_isp_by_imsi(imsi);
    if (isp == 1)
    {
        snprintf(mno, sizeof(mno), "%s", "china_mobile");
    }
    else if (isp == 2)
    {
        snprintf(mno, sizeof(mno), "%s", "china_united");
    }
    else if (isp == 3)
    {
        snprintf(mno, sizeof(mno), "%s", "china_telecom");
    }
    else 
    {
        return -1;    
    }

    if (esim_num == 1) {
        return cfg_set("esim1_mno", mno);
    } else if (esim_num == 2) {
        return cfg_set("esim2_mno", mno);
    } else if (esim_num == 3) {
        return cfg_set("esim3_mno", mno);
    }
    
    
    return -1;
}

/**
 * 读取网络接口流量统计
 * @param interface 网络接口名称 (如 "wan1")
 * @param rx_bytes 接收字节数指针
 * @param tx_bytes 发送字节数指针
 * @return 0 成功, -1 失败
 */
static int read_interface_traffic(const char *interface, unsigned long long *rx_bytes, unsigned long long *tx_bytes) {
    char rx_path[256], tx_path[256];
    FILE *rx_file, *tx_file;

    snprintf(rx_path, sizeof(rx_path), "/sys/class/net/%s/statistics/rx_bytes", interface);
    snprintf(tx_path, sizeof(tx_path), "/sys/class/net/%s/statistics/tx_bytes", interface);

    rx_file = fopen(rx_path, "r");
    if (!rx_file) {
        qrzl_err("Failed to open %s", rx_path);
        return -1;
    }

    tx_file = fopen(tx_path, "r");
    if (!tx_file) {
        qrzl_err("Failed to open %s", tx_path);
        fclose(rx_file);
        return -1;
    }

    if (fscanf(rx_file, "%llu", rx_bytes) != 1) {
        qrzl_err("Failed to read rx_bytes from %s", rx_path);
        fclose(rx_file);
        fclose(tx_file);
        return -1;
    }

    if (fscanf(tx_file, "%llu", tx_bytes) != 1) {
        qrzl_err("Failed to read tx_bytes from %s", tx_path);
        fclose(rx_file);
        fclose(tx_file);
        return -1;
    }

    fclose(rx_file);
    fclose(tx_file);
    return 0;
}

/**
 * 检查网络接口是否有流量活动
 * @param interface 网络接口名称
 * @param check_duration 检查持续时间(秒)
 * @return 1 有流量活动, 0 无流量活动, -1 检查失败
 */
static int check_interface_traffic_activity(const char *interface, int check_duration) {
    unsigned long long rx_bytes_before, tx_bytes_before;
    unsigned long long rx_bytes_after, tx_bytes_after;

    // 读取检查前的流量统计
    if (read_interface_traffic(interface, &rx_bytes_before, &tx_bytes_before) != 0) {
        qrzl_err("Failed to read initial traffic stats for %s", interface);
        return -1;
    }

    qrzl_log("Traffic before check - %s: RX=%llu, TX=%llu", interface, rx_bytes_before, tx_bytes_before);

    // 等待指定时间
    sleep(check_duration);

    // 读取检查后的流量统计
    if (read_interface_traffic(interface, &rx_bytes_after, &tx_bytes_after) != 0) {
        qrzl_err("Failed to read final traffic stats for %s", interface);
        return -1;
    }

    qrzl_log("Traffic after check - %s: RX=%llu, TX=%llu", interface, rx_bytes_after, tx_bytes_after);

    // 检查是否有流量变化
    if (rx_bytes_after != rx_bytes_before || tx_bytes_after != tx_bytes_before) {
        unsigned long long rx_diff = rx_bytes_after - rx_bytes_before;
        unsigned long long tx_diff = tx_bytes_after - tx_bytes_before;
        qrzl_log("Network activity detected on %s: RX_diff=%llu, TX_diff=%llu", interface, rx_diff, tx_diff);
        return 1; // 有流量活动
    }

    qrzl_log("No network activity detected on %s", interface);
    return 0; // 无流量活动
}

// 检测单个 IP 地址是否可达
static int check_network_with_ping(const char *host) {
    char command[256];
    snprintf(command, sizeof(command), "ping -c 1 -W 1 %s > /dev/null 2>&1", host);
    int ret = system(command);
    return ret == 0; // 返回 0 表示网络可达
}

// 检测多个 IP 地址，只要一个可达则网络正常
static int check_multiple_ips(const char **hosts, int num_hosts) {
    int i;
    for (i = 0; i < num_hosts; ++i) {
        qrzl_log("start ping %s", hosts[i]);
        if (check_network_with_ping(hosts[i])) {
            return 1; // 如果任意 IP 可达，则返回 1
        }
        sleep(1);
    }
    return 0; // 所有 IP 都不可达，返回 0
}

/**
 * 获取当前上网状态
 */
char *get_current_net_status() 
{
    int res;
    char ppp_status[32] = {0};
    memset(ppp_status, 0, sizeof(ppp_status));
    res = cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
    if (res != 0) {
        return "0";
    }
    if (strcmp(ppp_status, "ppp_connected") != 0) {
        return "0";
    }
    return "1";
}

/**
 * 设置卡槽状态
 * disable  0：禁用, 1: 启用
 * slot_number  卡槽号：1->ESIM1 2->ESIM2 3->外置卡
 */
void set_slot_state(int disable, int slot_number)
{
    switch (slot_number)
    {
    case 1:
        cfg_set("slot_esim1_is_enable", disable == 0 ? "0" : "1");
        break;
    case 2:
        cfg_set("slot_esim2_is_enable", disable == 0 ? "0" : "1");
        break;
    case 0:
        cfg_set("slot_esim3_is_enable", disable == 0 ? "0" : "1");
        break;
    default:
        break;
    }
}

/**
 * 优化的网络检测函数
 * 解决大流量下载时ping检测误判的问题
 * 通过检查网络接口流量变化来判断网络可用性
 */
int check_network() {
    g_qrzl_device_dynamic_data.is_test_net = 1;

    const char *dns_hosts[] = {
        "*********",      // 阿里云dns
        "************",   // 腾讯dns
        "*********",      // 阿里云备用dns
        "************"    // 腾讯云备用dns
    };

    int i, ret;
    char ppp_status_check_number[4] = {0};
    int ppp_check_num = 0;

    // 获取PPP状态检查次数配置
    ret = cfg_get_item("number_of_ppp_status_checks", ppp_status_check_number, sizeof(ppp_status_check_number));
    if (ret != 0) {
        qrzl_log("无法获取 number_of_ppp_status_checks value");
        ppp_check_num = 30;
    } else {
        // 确保字符串有效且非空
        if (ppp_status_check_number[0] == '\0' || strcmp(ppp_status_check_number, "0") == 0) {
            ppp_check_num = 30;
        } else {
            ppp_check_num = atoi(ppp_status_check_number);
            // 校验数值范围
            if (ppp_check_num <= 0 || ppp_check_num > 80) {
                ppp_check_num = 30;
            }
        }
    }
    qrzl_log("ppp_check_num: %d", ppp_check_num);

    // 等待PPP连接建立
    char ppp_status[32] = {0};
    for (i = 0; i < ppp_check_num; i++) {
        memset(ppp_status, 0, sizeof(ppp_status));
        cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
        qrzl_log("ppp_status: %s", ppp_status);
        if (0 == strcmp(ppp_status, "ppp_connected")) {
            sleep(1);
            break;
        }
        sleep(1);
    }

    // 首先检查网络接口流量活动
    qrzl_log("Checking network interface traffic activity...");
    int traffic_activity = check_interface_traffic_activity("wan1", 3);

    if (traffic_activity == 1) {
        // 有流量活动，网络可用
        qrzl_log("Network available: Traffic activity detected on wan1");
        g_qrzl_device_dynamic_data.is_test_net = 0;
        return 0;
    } else if (traffic_activity == -1) {
        // 流量检查失败，记录警告但继续ping检查
        qrzl_log("Warning: Failed to check traffic activity, falling back to ping test");
    } else {
        // 无流量活动，继续ping检查
        qrzl_log("No traffic activity detected, performing ping test...");
    }

    // 进行ping检查
    for (i = 0; i < 1; i++) {
        if (check_multiple_ips(dns_hosts, 4) == 1) {
            qrzl_log("Network available: Ping test successful");
            g_qrzl_device_dynamic_data.is_test_net = 0;
            return 0;
        }
        sleep(1);
    }

    qrzl_log("Network unavailable: Both traffic check and ping test failed");
    g_qrzl_device_dynamic_data.is_test_net = 0;
    return -1;
}

#ifdef JCV_HW_DZ803_V1_0
// DZ803充放电 支持控制放电开关
// ============ 电池放电管理 START ============
#define POWER_CTRL_PATH "/sys/class/leds/powerbank_en/brightness"

static atomic_int battery_status = 0;
static pthread_t discharge_thread;
static atomic_int discharge_thread_running = 0;

// 读取当前放电状态
int read_current_discharge_status_from_sysfs() {
    FILE* fp = fopen(POWER_CTRL_PATH, "r");
    if (!fp) {
        perror("Failed to open power control path");
        return -1;
    }

    int value = -1;
    if (fscanf(fp, "%d", &value) != 1) {
        fclose(fp);
        fprintf(stderr, "Failed to read power status\n");
        return -1;
    }

    fclose(fp);
    return value;
}

// 延迟关闭线程
void* delayed_discharge_task(void* arg) {
    uint64_t delay_time = 60 * 60; // 1小时
    int original_status = atomic_load(&battery_status);
    uint64_t i;
    for (i = 0; i < delay_time; ++i) {
        sleep(1);
        if (atomic_load(&battery_status) != original_status) {
            qrzl_log("[Discharge] 状态变更，中止延迟任务");
            discharge_thread_running = 0;
            return NULL;
        }
    }

    qrzl_log("[Discharge] 延迟完成，执行关闭电池放电操作");
    system("echo 0 > " POWER_CTRL_PATH);
    discharge_thread_running = 0;
    return NULL;
}

void control_battery_discharge(int status) {
#if defined (JCV_HW_DZ803_V1_0)
    atomic_store(&battery_status, status);

    int current_battery_status = read_current_discharge_status_from_sysfs();
    if (current_battery_status < 0) {
        qrzl_log("[Discharge] 放电文件读取错误，使用atomic加载当前状态");
        current_battery_status = atomic_load(&battery_status);
    }

    if (status == current_battery_status) {
        qrzl_log("[Discharge] 当前状态已是 %d，无需处理", status);
        return;
    }

    if (status == 0) {
        qrzl_log("[Discharge] 请求关闭电池放电，延迟中...");
        if (!discharge_thread_running) {
            discharge_thread_running = 1;
            pthread_create(&discharge_thread, NULL, delayed_discharge_task, NULL);
            pthread_detach(discharge_thread);
        } else {
            qrzl_log("[Discharge] 延迟线程正在运行，跳过");
        }
    } else {
        qrzl_log("[Discharge] 启动电池放电，立即执行");
        system("echo 1 > " POWER_CTRL_PATH);
    }
#endif
}

// ============ 电池放电管理 END ============


// ============ 根据网络状态控制 电池放电 监控线程 START ============

void* network_monitor_thread_func(void* arg) {

    while (1) {
        int charge_switch = read_current_discharge_status_from_sysfs();

        int total_esim_count = 1;
        int no_net_count = 0;

        char auto_switch_type[3] = {0};
        cfg_get_item("auto_switch_esim_type", auto_switch_type, sizeof(auto_switch_type));
        qrzl_log("[NetMon] auto_switch_esim_type : %s", auto_switch_type);

        switch (atoi(auto_switch_type)) {
            case 1: total_esim_count = 2; break;
            case 2: total_esim_count = 3; break;
            default: qrzl_log("[NetMon] 未知类型，默认1张卡"); break;
        }

        if (total_esim_count >= 1 && g_qrzl_device_dynamic_data.esim1_net_status == 0) no_net_count++;
        if (total_esim_count >= 2 && g_qrzl_device_dynamic_data.esim2_net_status == 0) no_net_count++;
        if (total_esim_count == 3 && g_qrzl_device_dynamic_data.esim3_net_status == 0) no_net_count++;

        qrzl_log("[NetMon] no_net_count : %d / total : %d", no_net_count, total_esim_count);
        qrzl_log("[NetMon] charge_switch : %d", charge_switch);
        qrzl_log("[NetMon] eSIM1:%d eSIM2:%d eSIM3:%d",
                 g_qrzl_device_dynamic_data.esim1_net_status,
                 g_qrzl_device_dynamic_data.esim2_net_status,
                 g_qrzl_device_dynamic_data.esim3_net_status);

        int decision = (no_net_count == total_esim_count) ? 0 : 1;

        // 获取当前网络是否禁用标识
        char net_disable_flag[3] = {0};
        cfg_get_item("qrzl_user_net_disconn", net_disable_flag, sizeof(net_disable_flag));

        if (atoi(net_disable_flag) == 1) {
            control_battery_discharge(0);
        } else {
            if (charge_switch != decision) {
                control_battery_discharge(decision);
            } else {
                qrzl_log("[NetMon] 放电状态未变化，跳过控制电池放电操作");
            }
        }


        sleep(30);  // 每30秒检查一次网络状态
    }

    return NULL;
}

void start_network_monitor_thread() {
    pthread_t net_thread;
    pthread_create(&net_thread, NULL, network_monitor_thread_func, NULL);
    pthread_detach(net_thread);
}

// ============ 根据网络状态控制 电池放电 监控线程 END ============
#endif

static void* auto_switch_esim_handler() 
{
    char usb_modetype[32] = {0};
    nv_get_item(NV_RO, "usb_modetype", usb_modetype, sizeof(usb_modetype));
    if (strcmp("user", usb_modetype) != 0)
    {
        qrzl_log("当前不是用户模式，不进行自动切卡");
        return NULL;
    }

	int ret;
	int switch_esim_detection_interval = 30; // 是否切卡检测间隔，单位秒
	char detection_interval_str[21] = {0};
	ret = cfg_get_item("switch_esim_detection_interval", detection_interval_str, sizeof(detection_interval_str));
	if (ret == 0)
	{
		int detection_interval = atoi(detection_interval_str);
		if (detection_interval > 0)
		{
			switch_esim_detection_interval = detection_interval;
		}
	}

	int esim1_status = 1;
	int esim2_status = 1;
	int next_esim_num = 1;
	char ziccid[21] = {0};
    char auto_switch_esim_type[3] = {0};
    int current_sim_index = -1;

    int kuyu_switch_out_card_flag = 0; // 【客户变量】- 酷鱼 切外卡标识
    (void)kuyu_switch_out_card_flag; /* 避免未使用变量警告 */

    sleep(20); // 等待网络初始化完成
	while (1)
	{
		sleep(switch_esim_detection_interval);
        memset(auto_switch_esim_type, 0, sizeof(auto_switch_esim_type));
        cfg_get_item("auto_switch_esim_type", auto_switch_esim_type, sizeof(auto_switch_esim_type));
        if (strcmp("0", auto_switch_esim_type) == 0)
        {
            qrzl_log("auto_switch_esim_type: %s 不开启断网自动切卡服务，开始下一次检测", auto_switch_esim_type);
            continue;
        }

#if defined(QRZL_KUYU_LOGIC_CODE) || defined(QRZL_AUTO_SWITCH_OUT_CARD_FIRST)
        qrzl_log("Kuyu -> into out_card priori logic...");
        if((get_device_current_sim_index() != 0) && kuyu_switch_out_card_flag == 0) {
            // 切换至外插卡
            qrzl_log("Kuyu -> current not out_card, begin switch out card.");
            switch_sim_card_not_restart(0);
            kuyu_switch_out_card_flag = 1; // 切过外插卡，每次开机都会默认寻外插卡是否可以上网
            sleep(5);
        }
#endif

		qrzl_log("开始检测esim卡是否有网");
        // 通过PING来检测
		if (check_network() == 0)
		{
            current_sim_index = get_device_current_sim_index();
			if (current_sim_index == 1) {
				g_qrzl_device_dynamic_data.esim1_net_status = 1;
			} else if (current_sim_index == 2) {
				g_qrzl_device_dynamic_data.esim2_net_status = 1;
			} else if (current_sim_index == 0) {
                g_qrzl_device_dynamic_data.esim3_net_status = 1;
            }
#ifdef QRZL_CUSTOM_TIANMU_LOGIC
           char first_flag[4] = {0};
            cfg_get_item("tianmu_first_attach_done", first_flag, sizeof(first_flag));
            if (atoi(first_flag) == 0) {
                cfg_set("tianmu_first_attach_done", "1");
                qrzl_log("[天目] 当前卡槽 %d 已驻网成功，写入标志", current_sim_index);
                cfg_set("auto_switch_esim_type", "0");
                qrzl_log("%d卡槽驻网成功,关闭自动切卡", current_sim_index);
            }
#endif
			continue;
		}

        memset(auto_switch_esim_type, 0, sizeof(auto_switch_esim_type));
        cfg_get_item("auto_switch_esim_type", auto_switch_esim_type, sizeof(auto_switch_esim_type));
        if (strcmp("0", auto_switch_esim_type) == 0)
        {
            qrzl_log("已关闭自动切卡，无需切卡");
            continue;
        }

        // ping 需要时间，如何云端下发切卡到不能上网的卡，会导致跳过一个卡去切，比如在卡1，云端切到外卡。这个时候ping的话，会以为是卡一没网，然后切到卡2
        current_sim_index = get_device_current_sim_index();

		qrzl_log("当前 %d: 无网络准备切卡", current_sim_index);

        if (strncmp(auto_switch_esim_type, "1", sizeof(auto_switch_esim_type)) == 0) {
            if (current_sim_index == 1) {
                next_esim_num = 2;
                g_qrzl_device_dynamic_data.esim1_net_status = 0;
            } else if(current_sim_index == 2) {
                next_esim_num = 1;
                g_qrzl_device_dynamic_data.esim2_net_status = 0;
            }
        } else if (strncmp(auto_switch_esim_type, "2", sizeof(auto_switch_esim_type)) == 0) {
#ifndef QRZL_CUSTOM_YIMING_LOGIC

            if (current_sim_index == 1) {
                g_qrzl_device_dynamic_data.esim1_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0) {
                    next_esim_num = 2;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0) {
                    next_esim_num = 0;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if(current_sim_index == 2) {
                g_qrzl_device_dynamic_data.esim2_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0) {
                    next_esim_num = 0;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0) {
                    next_esim_num = 1;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if (current_sim_index == 0) {
                g_qrzl_device_dynamic_data.esim3_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0) {
                    next_esim_num = 1;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0) {
                    next_esim_num = 2;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            }
#else 
            /**
             * 伊鸣 自动寻网逻辑 
             *  被禁用的卡不进入寻网逻辑
             */
            if (current_sim_index == 0) {
                g_qrzl_device_dynamic_data.esim3_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim1_is_enable == 1) {
                    next_esim_num = 1;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim2_is_enable == 1) {
                    next_esim_num = 2;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if (current_sim_index == 1) {
                g_qrzl_device_dynamic_data.esim1_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim2_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim2_is_enable == 1) {
                    next_esim_num = 2;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim3_is_enable == 1) {
                    next_esim_num = 0;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } else if(current_sim_index == 2) {
                g_qrzl_device_dynamic_data.esim2_net_status = 0;
                if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim3_is_enable == 1) {
                    next_esim_num = 0;
                } else if(strlen(g_qrzl_device_static_data.nvro_esim1_iccid) != 0 && g_qrzl_device_dynamic_data.slot_esim1_is_enable == 1) {
                    next_esim_num = 1;
                } else {
                    qrzl_log("没有可用的卡,不切卡");
                    continue;
                }
            } 
#endif
        }
        

		switch_sim_card_not_restart(next_esim_num);
		sleep(1);

        sleep(3);
		memset(ziccid, 0, sizeof(ziccid));
		cfg_get_item("ziccid", ziccid, sizeof(ziccid));
        if (strncmp("00000000000000000000", ziccid, sizeof(ziccid))  != 0) {
            cfg_set("modem_main_state", "modem_init_complete");
        } else {
            cfg_set("modem_main_state", "modem_sim_undetected");
        }

		cfg_set("user_initiate_disconnect", "0");  //用户主动连接状态，退出干预模式
        
// #ifndef QRZL_APP_CUSTOMIZATION_HX        
// 		ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_AT_CTL, MSG_CMD_PDP_ACT_REQ, 0, NULL, 0);
// #endif
        char ppp_status[32] = {0};
        cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
        int iConnectStatus = 0;
        if(0 == strcmp(ppp_status, "ppp_disconnected"))
        {
                iConnectStatus = 0;
        }
        else if(0 == strcmp(ppp_status, "ppp_connected"))
        {
                iConnectStatus = 1;
        }
        else if(0 == strcmp(ppp_status, "ppp_connecting"))
        {
                iConnectStatus = 2;
        }
        else if(0 == strcmp(ppp_status, "ppp_disconnecting"))
        {
                iConnectStatus = 3;
        }
        ipc_send_message(MODULE_ID_QRZL_APP, MODULE_ID_MMI, MSG_CMD_CHANNEL_CONNECT_STATUS, sizeof(iConnectStatus), (UCHAR *)&iConnectStatus,0);


		if (strncmp("00000000000000000000", ziccid, sizeof(ziccid))  == 0)
		{
			if (next_esim_num == 1)
			{
				qrzl_log("esim %d iccid = %s, esim1_status=0", next_esim_num, ziccid);
				esim1_status = 0;
			}
			else if (next_esim_num == 2)
			{
				qrzl_log("esim %d iccid = %s, esim2_status=0", next_esim_num, ziccid);
				esim2_status = 0;
			}
		}
		else
		{
            if (strncmp(auto_switch_esim_type, "1", sizeof(auto_switch_esim_type)) == 0)
            {
                if (esim1_status == 0 || esim2_status == 0)
                {
                    qrzl_log("有一个卡读不到，无需再切卡");
                    break;
                }
            }
            else if (strncmp(auto_switch_esim_type, "2", sizeof(auto_switch_esim_type)) == 0)
            {
                if (esim1_status == 0 && esim2_status == 0)
                {
                    qrzl_log("两个卡读不到，无需再切卡");
                    break;
                }
            }
		}
	}
	return NULL;
}


/**
 * 客户定制需求的初始化
 */
void customer_customization_requirements_init()
{
    int ret;
    char qrzl_user_net_disconn[2] = {0};
    ret = cfg_get_item("qrzl_user_net_disconn", qrzl_user_net_disconn, sizeof(qrzl_user_net_disconn));
    if (ret == 0 && strcmp("1", qrzl_user_net_disconn) == 0)
    {
        // 防火墙会在每次开机的时候充值，所以只需要禁用网络时才需要设置
        set_network_br0_disconnect(1);
    }

	// 设备启动时，假定两张卡都有网
	g_qrzl_device_dynamic_data.esim1_net_status = 1;
	g_qrzl_device_dynamic_data.esim2_net_status = 1;
    g_qrzl_device_dynamic_data.esim3_net_status = 1;

    // 启动自动切卡线程，但实际要不要切卡，在线程里面再处理
    int err;
    pthread_t auto_switch_esim_tid;
    err = pthread_create(&auto_switch_esim_tid, NULL, auto_switch_esim_handler, NULL);
    if (err != 0)
    {
        qrzl_err("create auto_switch_esim_handler pthread error code: %d", err);
    }

    // dz803 电池放电控制 
#if defined (JCV_HW_DZ803_V1_0)
	start_network_monitor_thread();
#endif

}

/**
 * 获取本地时间并根据指定格式返回
 * 参数:
 * format: 时间格式字符串 (如 "%Y-%m-%d %H:%M:%S")
 * buffer: 用于存储时间字符串的缓冲区
 * buffer_size: 缓冲区大小
 * 返回值:
 * 0: 成功
 * -1: 失败
*/
int get_local_time(const char *format, char *buffer, size_t buffer_size)
{
    if (format == NULL || buffer == NULL || buffer_size == 0) {
        return -1;
    }

    time_t raw_time;
    struct tm *time_info;

    // 获取当前时间
    time(&raw_time);

    // 转换为本地时间
    time_info = localtime(&raw_time);
    if (time_info == NULL) {
        return -1;
    }

    // 格式化时间字符串
    if (strftime(buffer, buffer_size, format, time_info) == 0) {
        // 缓冲区不足或格式化失败
        return -1;
    }

    return 0;
}

int set_network_br0_disconnect(int status)
{
    char nv_user_net_disconn[] = "qrzl_user_net_disconn";

    // 获取PDP方式
    char pdp_type[30] = {0};
    cfg_get_item("pdp_type" , pdp_type, sizeof(pdp_type));
    qrzl_log("current pdp_type: %s", pdp_type);
    
    if (status == 1)
    {
        qrzl_log("set_network_br0_disconnect disable br0 network");
        // 添加规则：禁止 br0 -> wan1 转发
        system("iptables-save | grep -q -- '-A FORWARD -i br0 -o wan1 -j DROP' || iptables -I FORWARD -i br0 -o wan1 -j DROP");
        // 添加规则：禁止 wan1 -> br0 转发
        system("iptables-save | grep -q -- '-A FORWARD -i wan1 -o br0 -j DROP' || iptables -I FORWARD -i wan1 -o br0 -j DROP");

        if (strstr(pdp_type, "v6")) {
            // 如果有IPv6则禁用IPv6的链路转发

            // 更彻底禁止 IPv6 转发
            system("sysctl -w net.ipv6.conf.all.forwarding=0");
            // 增加阻断 IPv6 INPUT/OUTPUT
            system("ip6tables -I OUTPUT -o wan1 -j DROP");
            system("ip6tables -I INPUT -i wan1 -j DROP");
        }

#ifndef QRZL_NET_DISABLE_NO_CHANGE_LED
#ifdef QRZL_NET_READCARDREDON_CONNECTEDGREENON_DISABLENETGREENBLINK
        system("echo timer > /sys/class/leds/modem_g_led/trigger"); // 打开网络绿灯定时器
        system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#else
#ifdef QRZL_CUSTOM_TIANMU_LOGIC
        system("echo none > /sys/class/leds/modem_g_led/trigger"); // 关闭网络绿灯定时器
        system("echo 0 > /sys/class/leds/modem_g_led/brightness"); // 关闭网络绿灯

        char ziccid[21] = {0};
        cfg_get_item("ziccid", ziccid, sizeof(ziccid));
        if (strncmp("00000000000000000000", ziccid, sizeof(ziccid)) != 0) {
            system("echo timer > /sys/class/leds/modem_r_led/trigger"); // 打开网络红灯定时器
            system("echo 1 > /sys/class/leds/modem_r_led/brightness"); // 打开网络红灯
        }
#else
        system("echo none > /sys/class/leds/modem_g_led/trigger"); // 关闭网络绿灯定时器
        system("echo 0 > /sys/class/leds/modem_g_led/brightness"); // 关闭网络绿灯

        char ziccid[21] = {0};
        cfg_get_item("ziccid", ziccid, sizeof(ziccid));
        if (strncmp("00000000000000000000", ziccid, sizeof(ziccid)) != 0) {
            system("echo none > /sys/class/leds/modem_r_led/trigger"); // 关闭网络红灯定时器
            system("echo 1 > /sys/class/leds/modem_r_led/brightness"); // 打开网络红灯
        }
#endif
#endif
#endif
        cfg_set(nv_user_net_disconn, "1");
    }
    else
    {
        qrzl_log("set_network_br0_disconnect enable br0 network");
        system("iptables -D FORWARD -i br0 -o wan1 -j DROP");
        system("iptables -D FORWARD -i wan1 -o br0 -j DROP");
        // ipv6
        if (strstr(pdp_type, "v6")) {
            // 恢复 IPv6 转发
            system("sysctl -w net.ipv6.conf.all.forwarding=1");

            // 删除 IPv6 DROP 规则
            system("ip6tables -D OUTPUT -o wan1 -j DROP");
            system("ip6tables -D INPUT -i wan1 -j DROP");
        }

#ifndef QRZL_NET_DISABLE_NO_CHANGE_LED
#if defined(QRZL_APP_CUSTOMIZATION_MY) || defined(QRZL_NET_CONNECTED_ALWAYS_GREEN) || defined(QRZL_NET_READCARDREDON_CONNECTEDGREENON_DISABLENETGREENBLINK) || defined(QRZL_CUSTOM_TIANMU_LOGIC)
        system("echo none > /sys/class/leds/modem_g_led/trigger"); // 关闭网络绿灯定时器
        system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#else 
        system("echo timer > /sys/class/leds/modem_g_led/trigger"); // 打开网络绿灯定时器
        system("echo 1 > /sys/class/leds/modem_g_led/brightness"); // 打开网络绿灯
#endif
        system("echo none > /sys/class/leds/modem_r_led/trigger"); // 关闭网络红灯定时器
        system("echo 0 > /sys/class/leds/modem_r_led/brightness"); // 关闭网络红灯
#endif
        cfg_set(nv_user_net_disconn, "0");
    }
    return 0;
}

// URL解码: 把 %xx 转成对应字符，把 + 转成空格
int url_decode(const char *src, char *dst, int dst_len) {
    char a, b;
    int i, j;
    int len = strlen(src);

    for (i = 0, j = 0; i < len && j < dst_len - 1; i++) {
        if (src[i] == '%' && i + 2 < len &&
            isxdigit((unsigned char)src[i+1]) &&
            isxdigit((unsigned char)src[i+2])) {
            a = src[i+1];
            b = src[i+2];
            a = (a >= 'A') ? (a & 0xdf) - 'A' + 10 : (a - '0');
            b = (b >= 'A') ? (b & 0xdf) - 'A' + 10 : (b - '0');
            dst[j++] = (char)(16*a + b);
            i += 2;
        } else if (src[i] == '+') {
            dst[j++] = ' ';
        } else {
            dst[j++] = src[i];
        }
    }
    dst[j] = '\0';
    return j;
}


int url_encode(const char *str, char *encoded)
{
    if (str == NULL || encoded == NULL) {
        return -1;
    }

    size_t len = strlen(str);

    char *p = encoded;
    size_t i;
    for (i = 0; i < len; i++) {
        unsigned char c = (unsigned char)str[i];
        // 如果字符是字母、数字或安全符号，则无需编码
        if (isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            *p++ = c;
        } else {
            // 非安全字符，编码为%HH
            sprintf(p, "%%%02X", c);
            p += 3;
        }
    }

    *p = '\0'; // 确保字符串以'\0'结尾
    return 0;
}

int valid_ipv4(const char *ip)
{
    struct in_addr addr;
    // 使用inet_pton来判断是否是有效的IPv4地址
    return inet_pton(AF_INET, ip, &addr) == 1;
}

void generate_random_string(char *dest, size_t dest_len) {
    // 随机数初始化
    srand(time(NULL));

    // 确保传入的字符串长度至少为1
    if (dest_len == 0) {
        dest[0] = '\0';
        return;
    }

    // 随机生成每个字符
    size_t i;
    for (i = 0; i < dest_len - 1; i++) {  // 留出一个位置给'\0'
        int rand_char = rand() % 62;  // 26大写字母 + 26小写字母 + 10数字
        if (rand_char < 26) {
            dest[i] = 'a' + rand_char;  // 小写字母
        } else if (rand_char < 52) {
            dest[i] = 'A' + rand_char - 26;  // 大写字母
        } else {
            dest[i] = '0' + rand_char - 52;  // 数字
        }
    }

    // 添加字符串结束符
    dest[dest_len] = '\0';
}

double get_device_uptime()
{
    FILE *fp;
    double uptime_seconds;

    // 打开 /proc/uptime 文件
    fp = fopen("/proc/uptime", "r");
    if (fp == NULL) {
        qrzl_err("Error opening /proc/uptime");
        return 0L;
    }

    // 读取系统的总运行时间（单位：秒）
    if (fscanf(fp, "%lf", &uptime_seconds) != 1) {
        qrzl_err("Error reading uptime");
        fclose(fp);
        return 0L;
    }
    fclose(fp);
    return uptime_seconds;
}

int get_device_charge_status()
{
    FILE *fp;
    char status[32];  // 用于存储充电状态

    int ret = 0;

    // 打开 /sys/class/power_supply/charger/status 文件
    fp = fopen("/sys/class/power_supply/charger/status", "r");
    if (fp == NULL) {
        qrzl_err("Error opening charging status file");
        return 0;
    }

    // 读取文件内容
    if (fgets(status, sizeof(status), fp) != NULL) {
        // 根据内容判断是否在充电
        qrzl_log("Charging status: %s", status);
        if (strncmp("Charging", status, 8) == 0) {
            ret = 1;
        }
    }
    // 关闭文件
    fclose(fp);

    return ret;
}

void sleep_ms(uint32_t milliseconds) {
    struct timespec ts;
    ts.tv_sec = milliseconds / 1000;
    ts.tv_nsec = (milliseconds % 1000) * 1000000;
    nanosleep(&ts, NULL);
}

uint8_t get_rsrp_percentage()
{
    char lte_rsrp[10] = {0};
    cfg_get_item("lte_rsrp", lte_rsrp, sizeof(lte_rsrp));
    int rsrp = atoi(lte_rsrp);
    
    uint8_t sim_signal_level = 0;
    if (rsrp >= -85) {
        sim_signal_level = 5;
    } else if (rsrp >= -95)
    {
        sim_signal_level = 4;
    } else if (rsrp >= -105)
    {
        sim_signal_level = 3;
    } else if (rsrp >= -115)
    {
        sim_signal_level = 2;
    } else if (rsrp >= -199)
    {
        sim_signal_level = 1;
    }
    sim_signal_level = sim_signal_level * 20;
    return sim_signal_level;
}

uint32_t get_now_utc_sec()
{
    struct timeval tv;
    struct tm *tm_info;
    (void)tm_info; /* 避免未使用变量警告 */

    // 获取当前的时间
    if (gettimeofday(&tv, NULL) != 0) {
        perror("gettimeofday");
        return 1;
    }

    return tv.tv_sec;
}


uint8_t get_remain_power()
{
    update_battery_vol_percent();
    char battery_vol_percent[10] = {0};
    cfg_get_item("battery_vol_percent", battery_vol_percent, sizeof(battery_vol_percent));
    return atoi(battery_vol_percent);
}

/**
 * 根据卡号设置该卡本月或今日的流量
 * flow: 用户传入的流量值（bytes）
 * sim_id: 用户传入要设置的卡（逻辑卡号）
 * m_or_d:【0代表day】【1代表month】
 */
void set_month_day_flow_by_sim(uint64_t flow, int sim_id, int m_or_d) {
    qrzl_log("qrzl_utils -> into set_month_day_flow_by_sim function.");
    switch (sim_id)
    {
        case 1:
            // ESIM1
            if(!m_or_d) {
                char esim1_flux_day_total[128] = {0};
                snprintf(esim1_flux_day_total, sizeof(esim1_flux_day_total), "%lld", flow);
                cfg_set("esim1_flux_day_total", esim1_flux_day_total);
            } else {
                char esim1_flux_month_total[128] = {0};
                snprintf(esim1_flux_month_total, sizeof(esim1_flux_month_total), "%lld", flow);
                cfg_set("esim1_flux_month_total", esim1_flux_month_total);
            }
            break;
        case 2:
            // ESIM2
#ifdef QRZL_ESIM2_ON_SIM_SLOT
            if(!m_or_d) {
                char rsim_flux_day_total[128] = {0};
                snprintf(rsim_flux_day_total, sizeof(rsim_flux_day_total), "%lld", flow);
                cfg_set("rsim_flux_day_total", rsim_flux_day_total);
            } else {
                char rsim_flux_month_total[128] = {0};
                snprintf(rsim_flux_month_total, sizeof(rsim_flux_month_total), "%lld", flow);
                cfg_set("rsim_flux_month_total", rsim_flux_month_total);
            }
            break;
#else
            if(!m_or_d) {
                char esim2_flux_day_total[128] = {0};
                snprintf(esim2_flux_day_total, sizeof(esim2_flux_day_total), "%lld", flow);
                cfg_set("esim2_flux_day_total", esim2_flux_day_total);
            } else {
                char esim2_flux_month_total[128] = {0};
                snprintf(esim2_flux_month_total, sizeof(esim2_flux_month_total), "%lld", flow);
                cfg_set("esim2_flux_month_total", esim2_flux_month_total);
            }
            break;
        case 0:
            // RSIM
            if(!m_or_d) {
                char rsim_flux_day_total[128] = {0};
                snprintf(rsim_flux_day_total, sizeof(rsim_flux_day_total), "%lld", flow);
                cfg_set("rsim_flux_day_total", rsim_flux_day_total);
            } else {
                char rsim_flux_month_total[128] = {0};
                snprintf(rsim_flux_month_total, sizeof(rsim_flux_month_total), "%lld", flow);
                cfg_set("rsim_flux_month_total", rsim_flux_month_total);
            }
            break;
#endif
    }
}


/**
 * 将 MAC 地址中的 : 替换为 -
 * 输入示例: "00:1A:2B:3C:4D:5E"
 * 输出示例: "00-1A-2B-3C-4D-5E"
 */
void convert_mac_colon_to_dash(const char *mac_input, char *mac_output, size_t output_size) {
    if (!mac_input || !mac_output || output_size < strlen(mac_input) + 1) {
        return;
    }

    size_t i;
    for (i = 0; i < strlen(mac_input) && i < output_size - 1; i++) {
        mac_output[i] = (mac_input[i] == ':') ? '-' : mac_input[i];
    }
    mac_output[i] = '\0';
}

/**
 * 将 MAC 地址中的 - 替换为 :
 * 输入示例: "00-1A-2B-3C-4D-5E"
 * 输出示例: "00:1A:2B:3C:4D:5E"
 */
void convert_mac_dash_to_colon(const char *mac_input, char *mac_output, size_t output_size) {
    if (!mac_input || !mac_output || output_size < strlen(mac_input) + 1) {
        return;
    }

    size_t i;
    for (i = 0; i < strlen(mac_input) && i < output_size - 1; i++) {
        mac_output[i] = (mac_input[i] == '-') ? ':' : mac_input[i];
    }
    mac_output[i] = '\0';
}


/**
 * 校验手机号
 */
int is_valid_phone(const char *phone) {
    if (strlen(phone) != 11)
        return 0;

    if (phone[0] != '1')
        return 0;

    if (phone[1] < '3' || phone[1] > '9')
        return 0;
    int i = 0;
    for (i = 0; i < 11; i++) {
        if (!isdigit(phone[i]))
            return 0;
    }

    return 1;
}


int connect_net_time(char *out_buf, size_t buf_len) {
    int ret = cfg_get_item("realtime_time", out_buf, buf_len);
    if (ret != 0) {
        qrzl_log("get realtime_time error!");
        snprintf(out_buf, buf_len, "0");
        return -1;
    }
    return 0;
}


/*****************************************************************************************************
 * @brief 将任意格式的MAC地址（可能含有':'或'-'或不含）标准化为指定格式
 *
 * @param input         输入MAC地址字符串（如 "3C:67:FD:74:D2:FB", "3c-67-fd-74-d2-fb", "3c67fd74d2fb"）
 * @param output        输出结果缓冲区
 * @param out_size      输出缓冲区大小，至少应为 18
 * @param format_char   输出格式：':' 表示冒号分隔，'-' 表示短横线分隔，'\0' 表示无分隔
 *
 * @return int 0 表示成功，-1 表示失败（如非法输入）
 *****************************************************************************************************/
int convert_mac_format(const char *input, char *output, size_t out_size, char format_char) {
    char temp[13] = {0}; // 临时存储12个16进制字符 + 结尾0
    int len = 0;

    if (!input || !output || out_size < 13 || (format_char && out_size < 18)) {
        return -1;
    }

    // 1. 提取16进制字符（忽略':' 或 '-'）
    int i;
    for ( i = 0; input[i] && len < 12; ++i) {
        if (isxdigit((unsigned char)input[i])) {
            temp[len++] = tolower((unsigned char)input[i]);
        }
    }

    if (len != 12) {
        return -1; // 不是合法MAC地址
    }

    // 2. 格式化输出
    if (format_char == '\0') {
        // 不带分隔符
        snprintf(output, out_size, "%s", temp);
    } else {
        // 带分隔符，格式 xx:xx:xx:xx:xx:xx
        // 17字节+1结尾0，确保缓冲区至少18
        snprintf(output, out_size,
            "%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c%c",
            temp[0], temp[1], format_char,
            temp[2], temp[3], format_char,
            temp[4], temp[5], format_char,
            temp[6], temp[7], format_char,
            temp[8], temp[9], format_char,
            temp[10], temp[11]);
    }

    return 0;
}


/************************************************************************************************************
 * @brief 判断指定 MAC 是否在认证记录中，且是否在 timeout 秒内未过期
 *
 * @param mac            要查询的 MAC 地址（支持带或不带冒号）
 * @param nv_auth_str    NVRAM 中的认证字符串，如 "aa:bb:cc:dd:ee:ff@1720043651;12:34:56:78:90:ab@1720043000"
 * @param timeout_sec    超时时间，单位秒
 *
 * @return 1 表示已认证且未过期，0 表示未认证或过期，-1 表示格式错误
 ************************************************************************************************************/
int is_mac_authenticated_with_timeout(const char *mac, const char *nv_auth_str, int timeout_sec) {
    if (!mac || !nv_auth_str || timeout_sec <= 0) {
        return -1;
    }

    char mac_clean[32] = {0};
    int idx = 0;
    // 去除 MAC 中的冒号和大小写统一
    int i;
    for (i = 0; mac[i] && idx < 12; ++i) {
        if (isxdigit(mac[i])) {
            mac_clean[idx++] = tolower(mac[i]);
        }
    }
    mac_clean[idx] = '\0';
    if (strlen(mac_clean) != 12) return -1;

    char tmp[1024];
    strncpy(tmp, nv_auth_str, sizeof(tmp));
    tmp[sizeof(tmp) - 1] = '\0';

    char *entry = strtok(tmp, ";");
    time_t now = time(NULL);

    while (entry) {
        char *sep = strchr(entry, '@');
        if (!sep) {
            entry = strtok(NULL, ";");
            continue;
        }

        *sep = '\0';
        char entry_mac[32] = {0};
        idx = 0;
        int i;
        for (i = 0; entry[i] && idx < 12; ++i) {
            if (isxdigit(entry[i])) {
                entry_mac[idx++] = tolower(entry[i]);
            }
        }
        entry_mac[idx] = '\0';

        if (strlen(entry_mac) != 12) {
            entry = strtok(NULL, ";");
            continue;
        }

        if (strcmp(mac_clean, entry_mac) == 0) {
            time_t ts = (time_t)atol(sep + 1);
            if (now - ts <= timeout_sec) {
                return 1; // ✅ 认证且未过期
            } else {
                return 0; // ❌ 已认证但过期
            }
        }

        entry = strtok(NULL, ";");
    }

    return 0; // ❌ 未认证
}

// 添加（追加或更新） 认证mac
int add_mac_to_auth_list(const char *mac, const char *old_nv, char *new_nv, size_t new_nv_len) {
    if (!mac || !old_nv || !new_nv || new_nv_len == 0) return -1;

    char mac_clean[32] = {0};
    int idx = 0;
    int i;
    for (i = 0; mac[i] && idx < 12; ++i) {
        if (isxdigit(mac[i])) {
            mac_clean[idx++] = tolower(mac[i]);
        }
    }
    mac_clean[idx] = '\0';
    if (strlen(mac_clean) != 12) return -1;

    time_t now = time(NULL);
    char tmp[1024];
    strncpy(tmp, old_nv, sizeof(tmp));
    tmp[sizeof(tmp) - 1] = '\0';

    new_nv[0] = '\0';
    int updated = 0;

    char *entry = strtok(tmp, ";");
    while (entry) {
        char *sep = strchr(entry, '@');
        if (!sep) {
            entry = strtok(NULL, ";");
            continue;
        }

        *sep = '\0';
        char entry_mac[32] = {0};
        idx = 0;
        int i;
        for (i = 0; entry[i] && idx < 12; ++i) {
            if (isxdigit(entry[i])) {
                entry_mac[idx++] = tolower(entry[i]);
            }
        }
        entry_mac[idx] = '\0';

        if (strcmp(mac_clean, entry_mac) == 0) {
            // 替换旧时间戳
            char line[64];
            snprintf(line, sizeof(line), "%s@%ld;", mac_clean, now);
            strncat(new_nv, line, new_nv_len - strlen(new_nv) - 1);
            updated = 1;
        } else {
            char line[64];
            snprintf(line, sizeof(line), "%s@%s;", entry_mac, sep + 1);
            strncat(new_nv, line, new_nv_len - strlen(new_nv) - 1);
        }

        entry = strtok(NULL, ";");
    }

    if (!updated) {
        char line[64];
        snprintf(line, sizeof(line), "%s@%ld;", mac_clean, now);
        strncat(new_nv, line, new_nv_len - strlen(new_nv) - 1);
    }

    // 移除末尾 ;
    size_t len = strlen(new_nv);
    if (len > 0 && new_nv[len - 1] == ';') new_nv[len - 1] = '\0';

    return 0;
}

// 删除指定mac
int remove_mac_from_auth_list(const char *mac, const char *old_nv, char *new_nv, size_t new_nv_len) {
    if (!mac || !old_nv || !new_nv || new_nv_len == 0) return -1;

    char mac_clean[32] = {0};
    int idx = 0;
    int i;
    for (i = 0; mac[i] && idx < 12; ++i) {
        if (isxdigit(mac[i])) {
            mac_clean[idx++] = tolower(mac[i]);
        }
    }
    mac_clean[idx] = '\0';
    if (strlen(mac_clean) != 12) return -1;

    char tmp[1024];
    strncpy(tmp, old_nv, sizeof(tmp));
    tmp[sizeof(tmp) - 1] = '\0';

    new_nv[0] = '\0';

    char *entry = strtok(tmp, ";");
    while (entry) {
        char *sep = strchr(entry, '@');
        if (!sep) {
            entry = strtok(NULL, ";");
            continue;
        }

        *sep = '\0';
        char entry_mac[32] = {0};
        idx = 0;
        int i;
        for (i = 0; entry[i] && idx < 12; ++i) {
            if (isxdigit(entry[i])) {
                entry_mac[idx++] = tolower(entry[i]);
            }
        }
        entry_mac[idx] = '\0';

        if (strcmp(mac_clean, entry_mac) != 0) {
            char line[64];
            snprintf(line, sizeof(line), "%s@%s;", entry_mac, sep + 1);
            strncat(new_nv, line, new_nv_len - strlen(new_nv) - 1);
        }

        entry = strtok(NULL, ";");
    }

    // 移除末尾 ;
    size_t len = strlen(new_nv);
    if (len > 0 && new_nv[len - 1] == ';') new_nv[len - 1] = '\0';

    return 0;
}


// 将域名解析成 IPv4 地址（支持多个 IP）
int resolve_domain_to_ipv4(const char *hostname, char ip_list[][INET_ADDRSTRLEN], int max_ips) {
    struct addrinfo hints, *res, *p;
    int status, count = 0;

    memset(&hints, 0, sizeof hints);
    hints.ai_family = AF_INET;      // 只要 IPv4
    hints.ai_socktype = SOCK_STREAM;

    if ((status = getaddrinfo(hostname, NULL, &hints, &res)) != 0) {
        fprintf(stderr, "getaddrinfo: %s\n", gai_strerror(status));
        return -1;
    }

    for (p = res; p != NULL && count < max_ips; p = p->ai_next) {
        struct sockaddr_in *ipv4 = (struct sockaddr_in *)p->ai_addr;
        inet_ntop(AF_INET, &(ipv4->sin_addr), ip_list[count], INET_ADDRSTRLEN);
        count++;
    }

    freeaddrinfo(res);
    return count;  // 返回找到的 IPv4 数量
}

// 空中写号初始化
void air_write_at_init()
{
    const char *t_profile = "\"TERMINAL_PROFILE\",199331811,285999116,2231435039,0,50397184,16384,1342177280,0";

    char *pstr = malloc(128);  // 给足够空间
    memset(pstr, 0, 128);
    char *args[] = { pstr };

    int ret = get_modem_info2("AT+ZSET=\"TERMINAL_PROFILE\"\r", "%s", (void**)args, 0, 100);
    if (ret == 0) {
        // 清除回车换行
        pstr[strcspn(pstr, "\r\n")] = '\0';

        qrzl_log("当前TERMINAL_PROFILE : %s", pstr);
        qrzl_log("预期TERMINAL_PROFILE : %s", t_profile);

        if (strcmp(t_profile, pstr) != 0) {
            qrzl_log("profile 不一致，进行更新....");
            int set_ret = get_modem_info2(
                "AT+ZSET=\"TERMINAL_PROFILE\",199331811,285999116,2231435039,0,50397184,16384,1342177280,0\r",
                NULL, NULL, 0, 100);
            if (set_ret == 0) {
                qrzl_log("profile 更新完成!");
            } else {
                qrzl_log("profile 更新失败，请重启重试!");
            }
        } else {
            qrzl_log("profile 一致!");
        }
        free(pstr);
    } else {
        qrzl_err("AT+ZSET=\"TERMINAL_PROFILE\" 执行失败!!");
        free(pstr);
    }
}


#define MAX_MAC_COUNT 50
#define MAX_MAC_LEN   32    // 每个MAC最大长度 "xx:xx:xx:xx:xx:xx"
#define NV_BUF_LEN    5048  // 存储缓冲区长度
/**
 * 更新已认证的mac到nv的通用函数

 * 因为nv值能存mac的最大长度只有51个，所以目前通过滑动窗口的方式来丢前用后
 *  
 * 先把要更新的mac地址加到当前本地mac中（不存在加入，存在不加入）
 * 
 * 如果更新完的的mac数量大于等于50，则从最后面开始读取50个mac重新存放到本地nv中
 * 
 * @param authed_nv_name 存放当前mac字符串的nv名称
 * @param new_mac_str 要更新的mac
 * @return 0 成功， -1 失败
 */
int update_authed_mac_list(const char *authed_nv_name, const char *new_mac_str)
{
    if (new_mac_str == NULL) {
        return -1;
    }

    char nv_buf[NV_BUF_LEN] = {0};
    char all_macs[MAX_MAC_COUNT * 2][MAX_MAC_LEN]; // 临时存放旧+新 MAC
    int mac_count = 0;
    int i;

    // 1. 读取旧的 nv
    cfg_get_item((char *)authed_nv_name, nv_buf, sizeof(nv_buf));
    qrzl_log("[DEBUG] 原始NV: %s\n", nv_buf);

    /**
     *  strtok 用一个 全局静态变量 来保存解析位置，所以：
            不能嵌套使用。
            多线程不安全。
        strtok_r 把保存解析位置的变量交给调用者（saveptr），因此：
            可以嵌套使用。
            可以在多线程环境下独立处理。
     */

    // 2. 拆分旧的 MAC
    {
        char *token, *saveptr;
        token = strtok_r(nv_buf, ";", &saveptr);
        while (token && mac_count < MAX_MAC_COUNT * 2) {
            strncpy(all_macs[mac_count], token, MAX_MAC_LEN - 1);
            all_macs[mac_count][MAX_MAC_LEN - 1] = '\0';
            mac_count++;
            token = strtok_r(NULL, ";", &saveptr);
        }
    }
    qrzl_log("[DEBUG] 已有MAC数量: %d\n", mac_count);

    // 3. 拆分新传入的 MAC
    {
        char tmp[NV_BUF_LEN];
        strncpy(tmp, new_mac_str, sizeof(tmp) - 1);
        tmp[sizeof(tmp) - 1] = '\0';

        char *token, *saveptr;
        token = strtok_r(tmp, ";", &saveptr);
        while (token && mac_count < MAX_MAC_COUNT * 2) {
            int exists = 0;
            for (i = 0; i < mac_count; i++) {
                if (strcmp(all_macs[i], token) == 0) {
                    exists = 1;
                    break;
                }
            }
            if (!exists) {
                strncpy(all_macs[mac_count], token, MAX_MAC_LEN - 1);
                all_macs[mac_count][MAX_MAC_LEN - 1] = '\0';
                mac_count++;
                qrzl_log("[DEBUG] 新增MAC: %s\n", token);
            } else {
                qrzl_log("[DEBUG] 跳过已存在MAC: %s\n", token);
            }
            token = strtok_r(NULL, ";", &saveptr);
        }
    }

    // 4. 如果超过 MAX_MAC_COUNT，只保留后面的
    int start = 0;
    if (mac_count > MAX_MAC_COUNT) {
        start = mac_count - MAX_MAC_COUNT;
        qrzl_log("[DEBUG] MAC超过%d个，丢弃前面%d个\n", MAX_MAC_COUNT, start);
    }

    // 5. 拼接回字符串
    char new_nv[NV_BUF_LEN] = {0};
    for (i = start; i < mac_count; i++) {
        strcat(new_nv, all_macs[i]);
        if (i < mac_count - 1) strcat(new_nv, ";");
    }

    // 6. 存回 nv
    qrzl_log("[DEBUG] 更新后的NV: %s\n", new_nv);
    cfg_set((char *)authed_nv_name, new_nv);

    // wifidog重新获取认证信息
#ifdef QRZL_WIFIDOG_ONELINK
    if (mac_count > 0) {
        system("wdctl restart");
    }
#endif

    return 0;
}

/**
 * 获取二次认证开关状态
 */
void get_authentic_switch(char *switch_str, size_t len)
{
    memset(switch_str, 0, len);
    int ret = cfg_get_item("qrzl_cloud_authentic_switch", switch_str, len);
    if(ret < 0) {
        qrzl_log("qrzl_cloud_authentic_switch get vermode fail\n");
    } else {
        qrzl_log("qrzl_cloud_authentic_switch get %s\n", switch_str);
    }
}

/**
 * 设置二次认证开关
 * @param switch_str "0" 关闭二次认证 "1" 开启二次认证
 */
void set_authentic_switch(const char *switch_str)
{
    qrzl_log("qrzl_cloud_authentic_switch set %s", switch_str);
    int ret = cfg_set("qrzl_cloud_authentic_switch", (char *)switch_str);
    if(ret < 0) {
        qrzl_log("qrzl_cloud_authentic_switch set vermode fail\n");
    } else {
        // 设置成功 清空认证文件
        system("echo clear > /proc/cjportal/auth");
        qrzl_log("Certification information has been cleared.");
        qrzl_log("qrzl_cloud_authentic_switch set success!\n");
    }
}

/**
 * IP白名单写入
 * 
 *  === Usage ===
	echo 'add_ipv4 ***********' > /proc/cjportal/whitelist
	echo 'clear_ipv4' > /proc/cjportal/whitelist
	echo 'add_ipv6 20014860:48600000:00000000:00008888' > /proc/cjportal/whitelist
	echo 'clear_ipv6' > /proc/cjportal/whitelist
	echo 'clear' > /proc/cjportal/whitelist
 */
#if 0  // 暂时注释掉未使用的函数
static int write_whiteIP_to_proc(const char *ip, int ip_type) {
    FILE *fp = fopen("/proc/cjportal/whitelist", "w");
    if (!fp) {
        perror("fopen");
        return -1;
    }

    if (ip_type == 1) {
        fprintf(fp, "add_ipv4 %s\n", ip);
    } else if (ip_type == 2) {
        fprintf(fp, "add_ipv6 %s\n", ip);
    } else {
        fclose(fp);
        return -1;
    }

    fclose(fp);
    return 0;
}
#endif

/**
 * 检查 DNS 是否可用，尝试解析一个公共域名
 */
static int dns_ready_check(void)
{
    struct addrinfo hints, *res = NULL;
    int ret;

    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET; // 只检查 IPv4
    hints.ai_socktype = SOCK_STREAM;

    ret = getaddrinfo("baidu.com", NULL, &hints, &res);
    if (ret == 0) {
        freeaddrinfo(res);
        return 1; // DNS 正常
    }
    return 0;
}

/**
 * 二次认证域名解析（优化版：包含 DNS 检查、配置项重试）
 */
void resolution_authentication_dns()
{
    char auth_white_domain[1024] = {0};
    char ip_list[200][INET_ADDRSTRLEN];
    int ip_count, i;
    (void)ip_count; /* 避免未使用变量警告 */
    int max_ips = 200;

    int retry = 50;       // DNS 最多重试 50 次
    int delay_ms = 2000; // 每次等待 2 秒

    char ppp_status[32] = {0};

    // ====== DNS 就绪检测 ======
    while (retry--) {
        memset(ppp_status, 0, sizeof(ppp_status));
        cfg_get_item("ppp_status", ppp_status, sizeof(ppp_status));
        qrzl_log("[DEBUG] ppp_status: %s", ppp_status);
        if (dns_ready_check() && 0 == strcmp(ppp_status, "ppp_connected")) {
            qrzl_log("[INFO] DNS service is ready");
            break;
        }
        qrzl_log("[WARN] DNS not ready, retry left: %d", retry);
        usleep(delay_ms * 1000);
    }
    if (retry <= 0) {
        qrzl_err("[FATAL] DNS unavailable, skip resolution");
        return;
    }

    // ====== 获取配置项（最多 3 次） ======
    retry = 3;
    while (retry--) {
        if (cfg_get_item("auth_white_domain", auth_white_domain, sizeof(auth_white_domain)) < 0) {
            qrzl_err("[ERROR] cfg_get_item(auth_white_domain) failed, retry left: %d", retry);
        } else if (strlen(auth_white_domain) > 0) {
            qrzl_log("[INFO] auth_white_domain: %s", auth_white_domain);
            break;
        } else {
            qrzl_log("[WARN] auth_white_domain empty, retry left: %d", retry);
        }

        if (retry > 0)
            usleep(delay_ms * 1000);
    }
    if (strlen(auth_white_domain) == 0) {
        qrzl_err("[FATAL] auth_white_domain is empty, skip DNS resolution");
        return;
    }

    // ====== 域名解析（支持多个域名） ======
    char *saveptr;
    char *token = strtok_r(auth_white_domain, ",", &saveptr);
    int total_count = 0;

    while (token && total_count < max_ips) {
        int n = resolve_domain_to_ipv4(token, &ip_list[total_count], max_ips - total_count);
        if (n > 0) total_count += n;
        token = strtok_r(NULL, ",", &saveptr);
    }

    for (i = 0; i < total_count; i++) {
        qrzl_log("[DEBUG] IPv4[%d]: %s", i, ip_list[i]);
#ifdef JCV_FEATURE_CAPTIVE_PORTAL_SERVER
        write_whiteIP_to_proc(ip_list[i], 1);
#endif
        // 写到指定nv
        cfg_set("auth_white_ip", ip_list[i]);
    }

    qrzl_log("[INFO] Total resolved white IPs: %d", total_count);
}

/**
 * 校验 MAC 地址格式是否正确
 * 支持格式: xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx
 * @param mac 要校验的 MAC 地址字符串
 * @return 1 格式正确，0 格式错误
 */
int is_valid_mac(const char *mac) {
    regex_t regex;
    const char *pattern = "^([0-9A-Fa-f]{2}([-:])){5}([0-9A-Fa-f]{2})$";
    if (regcomp(&regex, pattern, REG_EXTENDED | REG_NOSUB) != 0) {
        return 0; // regex 编译失败
    }
    int ret = regexec(&regex, mac, 0, NULL, 0);
    regfree(&regex);
    return !ret;
}