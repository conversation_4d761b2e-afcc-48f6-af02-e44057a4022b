/* 避免宏定义冲突，先包含系统头文件 */
#include <netinet/in.h>
#include <arpa/inet.h>

/* 避免宏重定义警告 */
#ifdef _POSIX_C_SOURCE
#undef _POSIX_C_SOURCE
#endif
#ifdef _GNU_SOURCE
#undef _GNU_SOURCE
#endif

#include "../inc/qrzl_common.h"
#include "../inc/qrzl_timer.h"
#include "../inc/modules/my_http_client.h"

#include "softap_api.h"
#include "nv_api.h"
#include "at_utils.h"

/* 设备控制定时器 */
static qrzl_timer_id_t g_device_info_timer = QRZL_INVALID_TIMER_ID;
static qrzl_timer_id_t g_device_status_timer = QRZL_INVALID_TIMER_ID;
static qrzl_timer_id_t g_config_check_timer = QRZL_INVALID_TIMER_ID;

/* 定时器回调函数 */
static void device_info_timer_callback(qrzl_timer_id_t timer_id, void *user_data);
static void device_status_timer_callback(qrzl_timer_id_t timer_id, void *user_data);
static void config_check_timer_callback(qrzl_timer_id_t timer_id, void *user_data);

/* 更新设备静态数据 */
void update_device_static_data(void) {
    /* 获取设备IMEI */
    char imei[64] = {0};
    if (cfg_get_item("imei", imei, sizeof(imei)) == 0) {
        QRZL_STRNCPY(g_qrzl_device_static_data.imei, imei,
                     sizeof(g_qrzl_device_static_data.imei));
    }

    /* 获取设备序列号 */
    char device_sn[64] = {0};
    if (cfg_get_item("device_sn", device_sn, sizeof(device_sn)) == 0) {
        QRZL_STRNCPY(g_qrzl_device_static_data.sn, device_sn,
                     sizeof(g_qrzl_device_static_data.sn));
    }

    /* 获取MAC地址 */
    char mac[32] = {0};
    if (cfg_get_item("mac", mac, sizeof(mac)) == 0) {
        QRZL_STRNCPY(g_qrzl_device_static_data.mac, mac,
                     sizeof(g_qrzl_device_static_data.mac));
    }

    /* 获取固件版本 */
    char fw_version[64] = {0};
    if (cfg_get_item("firmware_version", fw_version, sizeof(fw_version)) == 0) {
        QRZL_STRNCPY(g_qrzl_device_static_data.version, fw_version,
                     sizeof(g_qrzl_device_static_data.version));
    }

    /* 获取设备型号 */
    char model[64] = {0};
    if (cfg_get_item("model", model, sizeof(model)) == 0) {
        QRZL_STRNCPY(g_qrzl_device_static_data.model, model,
                     sizeof(g_qrzl_device_static_data.model));
    }
    /* 测试环境下的模拟数据 */
    QRZL_STRNCPY(g_qrzl_device_static_data.sn, "SN123456789",
                 sizeof(g_qrzl_device_static_data.sn));
    QRZL_STRNCPY(g_qrzl_device_static_data.imei, "123456789012345",
                 sizeof(g_qrzl_device_static_data.imei));
    QRZL_STRNCPY(g_qrzl_device_static_data.mac, "00:11:22:33:44:55",
                 sizeof(g_qrzl_device_static_data.mac));
    QRZL_STRNCPY(g_qrzl_device_static_data.model, "MZ804",
                 sizeof(g_qrzl_device_static_data.model));
    QRZL_STRNCPY(g_qrzl_device_static_data.version, "2.0.0-test",
                 sizeof(g_qrzl_device_static_data.version));

    qrzl_log_debug("Device static data updated");
}

/* 更新设备动态数据 */
void update_device_dynamic_data(void) {
    /* 获取电池电量 */
    char battery_str[16] = {0};
    if (cfg_get_item("battery_level", battery_str, sizeof(battery_str)) == 0) {
        g_qrzl_device_dynamic_data.battery_level = (uint8_t)atoi(battery_str);
    }

    /* 获取信号强度 */
    char signal_str[16] = {0};
    if (cfg_get_item("signal_strength", signal_str, sizeof(signal_str)) == 0) {
        g_qrzl_device_dynamic_data.signal_strength = (uint8_t)atoi(signal_str);
    }

    /* 获取网络类型 */
    char network_type[32] = {0};
    if (cfg_get_item("network_type", network_type, sizeof(network_type)) == 0) {
        g_qrzl_device_dynamic_data.network_type = (uint8_t)atoi(network_type);
    }

    /* 获取连接设备数 */
    char devices_str[16] = {0};
    if (cfg_get_item("connected_devices", devices_str, sizeof(devices_str)) == 0) {
        g_qrzl_device_dynamic_data.connected_devices = (uint32_t)atoi(devices_str);
    }

    /* 更新时间戳 */
    g_qrzl_device_dynamic_data.last_update_time = time(NULL);

    qrzl_log_debug("Device dynamic data updated");
}

/* 设备信息上报定时器回调 */
static void device_info_timer_callback(qrzl_timer_id_t timer_id, void *user_data) {
    (void)timer_id;
    (void)user_data;

    qrzl_log_debug("Device info timer triggered");

    /* 更新设备静态数据 */
    update_device_static_data();

    /* 上报设备信息 */
    my_http_report_device_info();
}

/* 设备状态上报定时器回调 */
static void device_status_timer_callback(qrzl_timer_id_t timer_id, void *user_data) {
    (void)timer_id;
    (void)user_data;

    qrzl_log_debug("Device status timer triggered");

    /* 更新设备动态数据 */
    update_device_dynamic_data();

    /* 上报设备状态 */
    my_http_report_device_status();
}

/* 配置检查定时器回调 */
static void config_check_timer_callback(qrzl_timer_id_t timer_id, void *user_data) {
    (void)timer_id;
    (void)user_data;

    qrzl_log_debug("Config check timer triggered");

    /* 获取设备配置 */
    my_http_get_device_config();
}

/* 初始化设备控制模块 */
int qrzl_device_control_init(void) {
    qrzl_log_info("Initializing device control module...");

    /* 初始化MY HTTP客户端 */
    if (my_http_client_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize MY HTTP client");
        return QRZL_ERROR;
    }

    /* 初始化设备数据 */
    update_device_static_data();
    update_device_dynamic_data();

    /* 创建定时器 */
    g_device_info_timer = qrzl_timer_periodic(QRZL_TIMER_INTERVAL_1HOUR,
                                             device_info_timer_callback, NULL);
    if (g_device_info_timer == QRZL_INVALID_TIMER_ID) {
        qrzl_log_error("Failed to create device info timer");
        return QRZL_ERROR;
    }

    g_device_status_timer = qrzl_timer_periodic(QRZL_TIMER_INTERVAL_5MIN,
                                               device_status_timer_callback, NULL);
    if (g_device_status_timer == QRZL_INVALID_TIMER_ID) {
        qrzl_log_error("Failed to create device status timer");
        return QRZL_ERROR;
    }

    g_config_check_timer = qrzl_timer_periodic(QRZL_TIMER_INTERVAL_30MIN,
                                              config_check_timer_callback, NULL);
    if (g_config_check_timer == QRZL_INVALID_TIMER_ID) {
        qrzl_log_error("Failed to create config check timer");
        return QRZL_ERROR;
    }

    qrzl_log_info("Device control module initialized successfully");
    return QRZL_SUCCESS;
}

/* 销毁设备控制模块 */
void qrzl_device_control_destroy(void) {
    qrzl_log_info("Destroying device control module...");

    /* 删除定时器 */
    if (g_device_info_timer != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_delete(g_device_info_timer);
        g_device_info_timer = QRZL_INVALID_TIMER_ID;
    }

    if (g_device_status_timer != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_delete(g_device_status_timer);
        g_device_status_timer = QRZL_INVALID_TIMER_ID;
    }

    if (g_config_check_timer != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_delete(g_config_check_timer);
        g_config_check_timer = QRZL_INVALID_TIMER_ID;
    }

    /* 销毁MY HTTP客户端 */
    my_http_client_destroy();

    qrzl_log_info("Device control module destroyed");
}
