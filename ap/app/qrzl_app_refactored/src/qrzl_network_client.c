#include "../inc/qrzl_network_client.h"
#include "../common_utils/http_client.h"
#include "../common_utils/mqtt_client.h"

/* 网络客户端内部结构 */
typedef struct qrzl_network_client {
    qrzl_client_id_t id;
    qrzl_client_type_t type;
    qrzl_client_state_t state;
    qrzl_simple_client_config_t config;

    /* 协议特定的客户端句柄 */
    union {
        mqtt_client_t mqtt;
        void *tcp;
    } handle;

    /* 回调函数 */
    qrzl_client_callbacks_t callbacks;

    struct qrzl_network_client *next;
} qrzl_network_client_t;

/* 全局网络客户端管理器 */
static struct {
    bool initialized;
    pthread_mutex_t mutex;
    qrzl_client_id_t next_client_id;
    qrzl_network_client_t *client_list;
} g_network_manager = {0};

/* 初始化网络客户端系统 */
int qrzl_network_init(void) {
    if (g_network_manager.initialized) {
        return QRZL_SUCCESS;
    }

    memset(&g_network_manager, 0, sizeof(g_network_manager));

    if (pthread_mutex_init(&g_network_manager.mutex, NULL) != 0) {
        qrzl_log_error("Failed to init network manager mutex: %s", strerror(errno));
        return QRZL_ERROR;
    }

    g_network_manager.next_client_id = 1;
    g_network_manager.initialized = true;

    qrzl_log_info("Network client system initialized");
    return QRZL_SUCCESS;
}

/* 销毁网络客户端系统 */
void qrzl_network_destroy(void) {
    if (!g_network_manager.initialized) {
        return;
    }

    /* 销毁所有客户端 */
    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = g_network_manager.client_list;
    while (client) {
        qrzl_network_client_t *next = client->next;

        /* 断开连接并销毁客户端 */
        if (client->type == QRZL_CLIENT_MQTT && client->handle.mqtt) {
            mqtt_client_destroy(client->handle.mqtt);
        }

        QRZL_FREE(client);
        client = next;
    }
    g_network_manager.client_list = NULL;
    pthread_mutex_unlock(&g_network_manager.mutex);

    pthread_mutex_destroy(&g_network_manager.mutex);
    g_network_manager.initialized = false;

    qrzl_log_info("Network client system destroyed");
}

/* 查找客户端 */
static qrzl_network_client_t* find_client(qrzl_client_id_t client_id) {
    qrzl_network_client_t *client = g_network_manager.client_list;
    while (client) {
        if (client->id == client_id) {
            return client;
        }
        client = client->next;
    }
    return NULL;
}

/* 创建网络客户端 */
qrzl_client_id_t qrzl_client_create(const qrzl_simple_client_config_t *config) {
    if (!config) {
        qrzl_log_error("Invalid client config");
        return QRZL_INVALID_CLIENT_ID;
    }

    qrzl_network_client_t *client = QRZL_CALLOC(1, sizeof(qrzl_network_client_t));
    if (!client) {
        qrzl_log_error("Failed to allocate client memory");
        return QRZL_INVALID_CLIENT_ID;
    }

    pthread_mutex_lock(&g_network_manager.mutex);

    client->id = g_network_manager.next_client_id++;
    client->type = config->type;
    client->state = QRZL_CLIENT_STATE_IDLE;
    client->config = *config;

    /* 根据类型创建特定的客户端 */
    if (config->type == QRZL_CLIENT_MQTT) {
        mqtt_callbacks_t mqtt_callbacks = {0};
        /* TODO: 设置MQTT回调函数 */

        client->handle.mqtt = mqtt_client_create(config->client_id, &mqtt_callbacks);
        if (!client->handle.mqtt) {
            QRZL_FREE(client);
            pthread_mutex_unlock(&g_network_manager.mutex);
            qrzl_log_error("Failed to create MQTT client");
            return QRZL_INVALID_CLIENT_ID;
        }
    }

    /* 添加到链表头部 */
    client->next = g_network_manager.client_list;
    g_network_manager.client_list = client;

    pthread_mutex_unlock(&g_network_manager.mutex);

    qrzl_log_debug("Network client %u created (type=%d)", client->id, config->type);
    return client->id;
}

/* 销毁网络客户端 */
int qrzl_client_destroy(qrzl_client_id_t client_id) {
    pthread_mutex_lock(&g_network_manager.mutex);

    qrzl_network_client_t *client = g_network_manager.client_list;
    qrzl_network_client_t *prev = NULL;

    while (client) {
        if (client->id == client_id) {
            /* 断开连接 */
            if (client->state == QRZL_CLIENT_STATE_CONNECTED) {
                if (client->type == QRZL_CLIENT_MQTT && client->handle.mqtt) {
                    mqtt_disconnect(client->handle.mqtt);
                }
            }

            /* 销毁特定客户端 */
            if (client->type == QRZL_CLIENT_MQTT && client->handle.mqtt) {
                mqtt_client_destroy(client->handle.mqtt);
            }

            /* 从链表中移除 */
            if (prev) {
                prev->next = client->next;
            } else {
                g_network_manager.client_list = client->next;
            }

            QRZL_FREE(client);
            pthread_mutex_unlock(&g_network_manager.mutex);
            qrzl_log_debug("Network client %u destroyed", client_id);
            return QRZL_SUCCESS;
        }
        prev = client;
        client = client->next;
    }

    pthread_mutex_unlock(&g_network_manager.mutex);
    qrzl_log_error("Network client %u not found", client_id);
    return QRZL_ERROR;
}

/* HTTP GET请求 */
int qrzl_http_get(qrzl_client_id_t client_id, const char *url,
                  char *response, size_t resp_size, uint32_t timeout_ms) {
    (void)timeout_ms; /* 避免未使用参数警告 */

    if (!url || !response || resp_size == 0) {
        return QRZL_ERROR;
    }

    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = find_client(client_id);

    if (!client || (client->type != QRZL_CLIENT_HTTP && client->type != QRZL_CLIENT_HTTPS)) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("Invalid HTTP client %u", client_id);
        return QRZL_ERROR;
    }

    pthread_mutex_unlock(&g_network_manager.mutex);

    /* 执行HTTP GET请求 */
    int result = http_get(url, response, resp_size);

    qrzl_log_debug("HTTP GET %s: %s", url, (result == HTTP_OK) ? "success" : "failed");
    return (result == HTTP_OK) ? QRZL_SUCCESS : QRZL_ERROR;
}

/* HTTP POST请求 */
int qrzl_http_post(qrzl_client_id_t client_id, const char *url, const char *body,
                   char *response, size_t resp_size, uint32_t timeout_ms) {
    (void)timeout_ms; /* 避免未使用参数警告 */

    if (!url || !body || !response || resp_size == 0) {
        return QRZL_ERROR;
    }

    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = find_client(client_id);

    if (!client || (client->type != QRZL_CLIENT_HTTP && client->type != QRZL_CLIENT_HTTPS)) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("Invalid HTTP client %u", client_id);
        return QRZL_ERROR;
    }

    pthread_mutex_unlock(&g_network_manager.mutex);

    /* 执行HTTP POST请求 */
    int result = http_post(url, body, response, resp_size);

    qrzl_log_debug("HTTP POST %s: %s", url, (result == HTTP_OK) ? "success" : "failed");
    return (result == HTTP_OK) ? QRZL_SUCCESS : QRZL_ERROR;
}

/* MQTT连接 */
int qrzl_mqtt_connect(qrzl_client_id_t client_id, const qrzl_mqtt_connect_params_t *params) {
    if (!params) {
        return QRZL_ERROR;
    }

    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = find_client(client_id);

    if (!client || client->type != QRZL_CLIENT_MQTT) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("Invalid MQTT client %u", client_id);
        return QRZL_ERROR;
    }

    if (!client->handle.mqtt) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("MQTT client handle is NULL");
        return QRZL_ERROR;
    }

    /* 转换参数格式 */
    mqtt_connect_params_t mqtt_params = {0};
    mqtt_params.host = params->host;
    mqtt_params.port = params->port;
    mqtt_params.client_id = params->client_id;
    mqtt_params.username = params->username;
    mqtt_params.password = params->password;
    mqtt_params.keepalive = params->keepalive;
    mqtt_params.clean_session = params->clean_session;
    mqtt_params.use_ssl = params->use_ssl;

    mqtt_client_t mqtt_client = client->handle.mqtt;
    pthread_mutex_unlock(&g_network_manager.mutex);

    /* 执行MQTT连接 */
    int result = mqtt_connect(mqtt_client, &mqtt_params);

    if (result == MQTT_OK) {
        pthread_mutex_lock(&g_network_manager.mutex);
        client = find_client(client_id);
        if (client) {
            client->state = QRZL_CLIENT_STATE_CONNECTED;
        }
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_debug("MQTT client %u connected", client_id);
        return QRZL_SUCCESS;
    } else {
        qrzl_log_error("MQTT client %u connect failed", client_id);
        return QRZL_ERROR;
    }
}

/* MQTT断开连接 */
int qrzl_mqtt_disconnect(qrzl_client_id_t client_id) {
    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = find_client(client_id);

    if (!client || client->type != QRZL_CLIENT_MQTT) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("Invalid MQTT client %u", client_id);
        return QRZL_ERROR;
    }

    if (!client->handle.mqtt) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        return QRZL_SUCCESS;
    }

    mqtt_client_t mqtt_client = client->handle.mqtt;
    client->state = QRZL_CLIENT_STATE_DISCONNECTED;
    pthread_mutex_unlock(&g_network_manager.mutex);

    /* 执行MQTT断开连接 */
    int result = mqtt_disconnect(mqtt_client);

    qrzl_log_debug("MQTT client %u disconnected", client_id);
    return (result == MQTT_OK) ? QRZL_SUCCESS : QRZL_ERROR;
}

/* MQTT发布消息 */
int qrzl_mqtt_publish(qrzl_client_id_t client_id, const qrzl_mqtt_message_t *message) {
    if (!message || !message->topic || !message->payload) {
        return QRZL_ERROR;
    }

    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = find_client(client_id);

    if (!client || client->type != QRZL_CLIENT_MQTT) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("Invalid MQTT client %u", client_id);
        return QRZL_ERROR;
    }

    if (!client->handle.mqtt || client->state != QRZL_CLIENT_STATE_CONNECTED) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("MQTT client %u not connected", client_id);
        return QRZL_ERROR;
    }

    mqtt_client_t mqtt_client = client->handle.mqtt;
    pthread_mutex_unlock(&g_network_manager.mutex);

    /* 执行MQTT发布 */
    int result = mqtt_publish(mqtt_client, message->topic, message->payload,
                             message->payload_len, (mqtt_qos_t)message->qos, message->retained);

    if (result >= 0) {
        qrzl_log_debug("MQTT message published to %s", message->topic);
        return QRZL_SUCCESS;
    } else {
        qrzl_log_error("MQTT publish failed");
        return QRZL_ERROR;
    }
}

/* MQTT订阅主题 */
int qrzl_mqtt_subscribe(qrzl_client_id_t client_id, const char *topic, qrzl_mqtt_qos_t qos) {
    if (!topic) {
        return QRZL_ERROR;
    }

    pthread_mutex_lock(&g_network_manager.mutex);
    qrzl_network_client_t *client = find_client(client_id);

    if (!client || client->type != QRZL_CLIENT_MQTT) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("Invalid MQTT client %u", client_id);
        return QRZL_ERROR;
    }

    if (!client->handle.mqtt || client->state != QRZL_CLIENT_STATE_CONNECTED) {
        pthread_mutex_unlock(&g_network_manager.mutex);
        qrzl_log_error("MQTT client %u not connected", client_id);
        return QRZL_ERROR;
    }

    mqtt_client_t mqtt_client = client->handle.mqtt;
    pthread_mutex_unlock(&g_network_manager.mutex);

    /* 执行MQTT订阅 */
    int result = mqtt_subscribe(mqtt_client, topic, (mqtt_qos_t)qos);

    if (result == MQTT_OK) {
        qrzl_log_debug("MQTT subscribed to %s", topic);
        return QRZL_SUCCESS;
    } else {
        qrzl_log_error("MQTT subscribe failed");
        return QRZL_ERROR;
    }
}
