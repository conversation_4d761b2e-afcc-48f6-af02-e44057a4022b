/* 避免宏定义冲突，先包含系统头文件 */
#include <netinet/in.h>
#include <arpa/inet.h>

/* 避免宏重定义警告 */
#ifdef _POSIX_C_SOURCE
#undef _POSIX_C_SOURCE
#endif
#ifdef _GNU_SOURCE
#undef _GNU_SOURCE
#endif

#include "../inc/qrzl_common.h"
#include "../inc/qrzl_timer.h"
#include "../inc/qrzl_thread_mgr.h"
#include "../inc/qrzl_signal_handler.h"
#include "../inc/qrzl_network_client.h"
#include "../inc/modules/my_http_client.h"

#include "softap_api.h"
#include "nv_api.h"

/* 全局变量定义 */
volatile qrzl_app_state_t g_qrzl_app_state = QRZL_APP_STATE_INIT;
volatile sig_atomic_t g_qrzl_shutdown_flag = 0;

struct qrzl_device_static_data g_qrzl_device_static_data;
struct qrzl_device_dynamic_data g_qrzl_device_dynamic_data;

/* 消息队列ID */
static int g_qrzl_msg_queue_id = -1;

/* 定时器ID */
static qrzl_timer_id_t g_cloud_request_timer = QRZL_INVALID_TIMER_ID;
static qrzl_timer_id_t g_device_monitor_timer = QRZL_INVALID_TIMER_ID;
static qrzl_timer_id_t g_network_check_timer = QRZL_INVALID_TIMER_ID;

/* 线程ID */
static qrzl_thread_id_t g_cloud_client_thread = QRZL_INVALID_THREAD_ID;
static qrzl_thread_id_t g_device_monitor_thread = QRZL_INVALID_THREAD_ID;
static qrzl_thread_id_t g_message_handler_thread = QRZL_INVALID_THREAD_ID;

/* 函数声明 */
static int qrzl_app_init(void);
static void qrzl_app_cleanup(void);
static int qrzl_create_msg_queue(int module_id);
static void qrzl_handle_message(const qrzl_msg_buf_t *msg);
static void qrzl_cloud_request_callback(qrzl_timer_id_t timer_id, void *user_data);
static void qrzl_device_monitor_callback(qrzl_timer_id_t timer_id, void *user_data);
static void qrzl_network_check_callback(qrzl_timer_id_t timer_id, void *user_data);

/* 外部函数声明 */
extern int qrzl_device_control_init(void);
extern void qrzl_device_control_destroy(void);
extern void update_device_static_data(void);
extern void update_device_dynamic_data(void);

/* 线程函数声明 */
QRZL_THREAD_FUNC_DECLARE(cloud_client);
QRZL_THREAD_FUNC_DECLARE(device_monitor);
QRZL_THREAD_FUNC_DECLARE(message_handler);

/* 日志打印函数 */
void qrzl_log_print(qrzl_log_level_t level, const char *file, int line, 
                   const char *fmt, ...) {
    const char *level_str[] = {"ERROR", "WARN", "INFO", "DEBUG"};
    char timestamp[32];
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);
    
    printf("[%s] [%s] [%s:%d] ", timestamp, level_str[level], 
           strrchr(file, '/') ? strrchr(file, '/') + 1 : file, line);
    
    va_list args;
    va_start(args, fmt);
    vprintf(fmt, args);
    va_end(args);
    
    printf("\n");
    fflush(stdout);
}

/* 设置进程名称 */
void qrzl_set_process_name(const char *name) {
    if (name) {
        prctl(PR_SET_NAME, name, 0, 0, 0);
    }
}

/* 设置线程名称 */
void qrzl_set_thread_name(const char *name) {
    qrzl_set_process_name(name);
}

/* 创建消息队列 */
static int qrzl_create_msg_queue(int module_id) {
    int msq_id = msgget(module_id, IPC_CREAT | 0600);
    if (msq_id == -1) {
        qrzl_log_error("Failed to create msg queue module_id=%d, errno=%d",
                       module_id, errno);
    }
    return msq_id;
}

/* 消息接收包装函数 - 保持与原qrzl_app兼容 */
static ssize_t qrzl_msg_recv(int msqid, void* msgp, size_t msgsz,
                             long msgtype, int msgflag) {
    return msgrcv(msqid, msgp, msgsz, msgtype, msgflag);
}

/* 处理消息 */
static void qrzl_handle_message(const qrzl_msg_buf_t *msg) {
    if (!msg) {
        return;
    }

    switch (msg->usMsgCmd) {
        case MSG_CMD_QRZL_APP_SET_BAND:
            qrzl_log_info("Received SET_BAND message");
            /* TODO: 处理频段设置 */
            break;
            
        case MSG_CMD_QRZL_APP_WIFI_CONNECTED:
            qrzl_log_info("Received WIFI_CONNECTED message");
            /* TODO: 处理WiFi连接事件 */
            break;
            
        case MSG_CMD_QRZL_APP_WIFI_DISCONNECT:
            qrzl_log_info("Received WIFI_DISCONNECT message");
            /* TODO: 处理WiFi断开事件 */
            break;
            
        default:
            qrzl_log_warn("Unhandled message command: %d", msg->usMsgCmd);
            break;
    }
}

/* 云端请求定时器回调 */
static void qrzl_cloud_request_callback(qrzl_timer_id_t timer_id, void *user_data) {
    (void)timer_id;   /* 避免未使用参数警告 */
    (void)user_data;  /* 避免未使用参数警告 */
    qrzl_log_debug("Cloud request timer triggered");
    /* TODO: 触发云端请求 */
}

/* 设备监控定时器回调 */
static void qrzl_device_monitor_callback(qrzl_timer_id_t timer_id, void *user_data) {
    (void)timer_id;   /* 避免未使用参数警告 */
    (void)user_data;  /* 避免未使用参数警告 */
    qrzl_log_debug("Device monitor timer triggered");
    /* TODO: 更新设备状态 */
}

/* 网络检查定时器回调 */
static void qrzl_network_check_callback(qrzl_timer_id_t timer_id, void *user_data) {
    (void)timer_id;   /* 避免未使用参数警告 */
    (void)user_data;  /* 避免未使用参数警告 */
    qrzl_log_debug("Network check timer triggered");
    /* TODO: 检查网络状态 */
}

/* 云端客户端线程 */
QRZL_THREAD_FUNC_BEGIN(cloud_client)
    qrzl_log_info("Cloud client thread running");
    (void)thread_id;  /* 避免未使用变量警告 */

#ifdef QRZL_HTTP_CLIENT_XUNJI
    /* 启动XUNJI云端客户端 (MY客户实现) */
    xunji_cloud_client_start();
#else
    /* 其他客户的云端客户端逻辑 */
    while (!qrzl_thread_should_stop(thread_id)) {
        /* TODO: 其他云端客户端逻辑 */

        /* 使用线程安全的睡眠 */
        if (!qrzl_thread_sleep(thread_id, 1)) {
            break; /* 被中断 */
        }
    }
#endif
QRZL_THREAD_FUNC_END(cloud_client)

/* 设备监控线程 */
QRZL_THREAD_FUNC_BEGIN(device_monitor)
    qrzl_log_info("Device monitor thread running");
    
    while (!qrzl_thread_should_stop(thread_id)) {
        /* TODO: 设备监控逻辑 */
        
        if (!qrzl_thread_sleep(thread_id, 5)) {
            break;
        }
    }
QRZL_THREAD_FUNC_END(device_monitor)

/* 消息处理线程 */
QRZL_THREAD_FUNC_BEGIN(message_handler)
    qrzl_msg_buf_t msg;
    
    qrzl_log_info("Message handler thread running");
    
    while (!qrzl_thread_should_stop(thread_id)) {
        memset(&msg, 0, sizeof(msg));
        
        /* 非阻塞接收消息 - 使用兼容的qrzl_msg_recv */
        ssize_t ret = qrzl_msg_recv(g_qrzl_msg_queue_id, &msg,
                                   sizeof(msg) - sizeof(long), 0, IPC_NOWAIT);
        
        if (ret > 0) {
            qrzl_log_debug("Received message: %d", msg.usMsgCmd);
            qrzl_handle_message(&msg);
        } else if (errno != ENOMSG) {
            qrzl_log_error("msgrcv failed: %s", strerror(errno));
        }
        
        /* 短暂睡眠避免CPU占用过高 */
        if (!qrzl_thread_sleep_ms(thread_id, 100)) {
            break;
        }
    }
QRZL_THREAD_FUNC_END(message_handler)

/* 应用初始化 */
static int qrzl_app_init(void) {
    qrzl_log_info("Initializing QRZL application...");
    
    /* 初始化各个子系统 */
    if (qrzl_timer_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize timer system");
        return QRZL_ERROR;
    }
    
    if (qrzl_thread_mgr_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize thread manager");
        return QRZL_ERROR;
    }
    
    if (qrzl_signal_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize signal handler");
        return QRZL_ERROR;
    }
    
    if (qrzl_network_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize network client");
        return QRZL_ERROR;
    }

    if (qrzl_device_control_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize device control");
        return QRZL_ERROR;
    }
    
    /* 注册默认信号处理器 */
    qrzl_signal_register_defaults();
    
    /* 创建消息队列 */
    g_qrzl_msg_queue_id = qrzl_create_msg_queue(MODULE_ID_QRZL_APP);
    if (g_qrzl_msg_queue_id < 0) {
        qrzl_log_error("Failed to create message queue");
        return QRZL_ERROR;
    }
    
    /* 创建定时器 */
    g_cloud_request_timer = qrzl_timer_periodic(QRZL_TIMER_INTERVAL_5MIN,
                                               qrzl_cloud_request_callback, NULL);
    g_device_monitor_timer = qrzl_timer_periodic(QRZL_TIMER_INTERVAL_30S,
                                                qrzl_device_monitor_callback, NULL);
    g_network_check_timer = qrzl_timer_periodic(QRZL_TIMER_INTERVAL_1MIN,
                                               qrzl_network_check_callback, NULL);
    
    /* 创建线程 */
    g_cloud_client_thread = qrzl_thread_create(QRZL_THREAD_TYPE_CLOUD_CLIENT,
                                              QRZL_THREAD_NAME_CLOUD_HTTP,
                                              qrzl_thread_cloud_client, NULL);
    
    g_device_monitor_thread = qrzl_thread_create(QRZL_THREAD_TYPE_DEVICE_MONITOR,
                                                QRZL_THREAD_NAME_DEVICE_CTRL,
                                                qrzl_thread_device_monitor, NULL);
    
    g_message_handler_thread = qrzl_thread_create(QRZL_THREAD_TYPE_MESSAGE_HANDLER,
                                                 QRZL_THREAD_NAME_MSG_HANDLER,
                                                 qrzl_thread_message_handler, NULL);
    
    /* 启动线程 */
    qrzl_thread_start(g_cloud_client_thread);
    qrzl_thread_start(g_device_monitor_thread);
    qrzl_thread_start(g_message_handler_thread);
    
    g_qrzl_app_state = QRZL_APP_STATE_RUNNING;
    qrzl_log_info("QRZL application initialized successfully");

    return QRZL_SUCCESS;
}

/* 应用清理 */
static void qrzl_app_cleanup(void) {
    qrzl_log_info("Cleaning up QRZL application...");

    g_qrzl_app_state = QRZL_APP_STATE_STOPPING;

    /* 停止定时器 */
    if (g_cloud_request_timer != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_delete(g_cloud_request_timer);
    }
    if (g_device_monitor_timer != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_delete(g_device_monitor_timer);
    }
    if (g_network_check_timer != QRZL_INVALID_TIMER_ID) {
        qrzl_timer_delete(g_network_check_timer);
    }

    /* 停止线程 */
    if (g_cloud_client_thread != QRZL_INVALID_THREAD_ID) {
        qrzl_thread_stop(g_cloud_client_thread, 3000);
    }
    if (g_device_monitor_thread != QRZL_INVALID_THREAD_ID) {
        qrzl_thread_stop(g_device_monitor_thread, 3000);
    }
    if (g_message_handler_thread != QRZL_INVALID_THREAD_ID) {
        qrzl_thread_stop(g_message_handler_thread, 3000);
    }

    /* 清理消息队列 */
    if (g_qrzl_msg_queue_id >= 0) {
        msgctl(g_qrzl_msg_queue_id, IPC_RMID, NULL);
    }

    /* 销毁各个子系统 */
    qrzl_device_control_destroy();
    qrzl_network_destroy();
    qrzl_signal_destroy();
    qrzl_thread_mgr_destroy();
    qrzl_timer_destroy();

    g_qrzl_app_state = QRZL_APP_STATE_STOPPED;
    qrzl_log_info("QRZL application cleanup completed");
}

/* 主函数 */
int main(int argc, char *argv[]) {
    (void)argc;  /* 避免未使用参数警告 */
    (void)argv;  /* 避免未使用参数警告 */
    int ret = 0;

    /* 初始化全局数据结构 */
    memset(&g_qrzl_device_static_data, 0, sizeof(g_qrzl_device_static_data));
    memset(&g_qrzl_device_dynamic_data, 0, sizeof(g_qrzl_device_dynamic_data));

    /* 设置进程名称 */
    qrzl_set_process_name("qrzl_app");

    qrzl_log_info("QRZL Application starting...");
    qrzl_log_info("Version: 2.0.0 (Refactored)");
    qrzl_log_info("Build time: %s %s", __DATE__, __TIME__);

    /* 初始化应用 */
    if (qrzl_app_init() != QRZL_SUCCESS) {
        qrzl_log_error("Failed to initialize application");
        ret = 1;
        goto cleanup;
    }

    qrzl_log_info("QRZL Application started successfully");
    qrzl_log_info("Press Ctrl+C to stop gracefully");

    /* 主循环：等待退出信号 */
    while (!qrzl_signal_is_shutdown_requested()) {
        /* 检查应用状态 */
        if (g_qrzl_app_state == QRZL_APP_STATE_STOPPING) {
            break;
        }

        /* 短暂睡眠，避免CPU占用过高 */
        qrzl_timer_sleep_ms(1000);
    }

    qrzl_log_info("Shutdown signal received, stopping application...");

cleanup:
    /* 清理资源 */
    qrzl_app_cleanup();

    qrzl_log_info("QRZL Application stopped");
    return ret;
}
