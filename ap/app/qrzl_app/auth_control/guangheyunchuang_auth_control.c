#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define KEY_ONE_LINK_GET_TOKEN_URL       "ONE_LINK_customers_get_token_url"
#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define CURL_REQUEST_ERROR -1
#define CURL_REQUEST_OK 0

static char one_link_customers_get_token_url[256] = {0};

// 获取当前本地时间字符串，格式："YYYY-MM-DD HH:MM:SS"
static void get_local_datetime(char *buffer, size_t size) {
    if (!buffer || size < 20) return;  // 安全检查：最小需 20 字节

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}


static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

static int http_send_post_request(const char *url, const char *body, char *response)
{
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);
    qrzl_log("http request body: %s", body);

    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        // No SSL verification for HTTP requests
        // curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        // curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 设置POST请求的内容
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);

        // 设置HTTP头（告诉服务器这是JSON数据）
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        // 清理
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (res != CURLE_OK)
        {
            return -1;
        }
        return 0;
    }
    return -1;
}

static int init_customers_info()
{
    //由于光合电信移动认证用的同一个接口，所以随便取一个URL就行
    int res_url = cfg_get_item(KEY_ONE_LINK_GET_TOKEN_URL, one_link_customers_get_token_url, sizeof(one_link_customers_get_token_url));
    if (res_url != 0) {
        qrzl_log("Configuration item %s get error!", KEY_ONE_LINK_GET_TOKEN_URL);
    }
}
/**
 * 光合 上报设备上下线
 * @param push_type 上报类型 0 下线， 1 上线
 * @param terminalMac 终端MAC
 * @return 0 成功  其他的值不一定失败，未认证的设备上报会返回别的，代码结构问题，以后待优化
 */
int ghyc_report_device_line_state(int push_type, char *terminalMac)
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    //拼接上下线URL
    const char *interface_str = (push_type == 0) ? "/api/terminalOffline" : "/api/terminalOnline";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);

    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};
    char imei_str[64] = {0};
    char timestamp[64] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), '-');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), '-');
    snprintf(imei_str, sizeof(imei_str), "%s", g_qrzl_device_static_data.imei);
    // 获取当前时间戳
    get_local_datetime(timestamp, sizeof(timestamp));
    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str); 
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_str); 
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    cJSON_AddStringToObject(root, "imei", imei_str);
    cJSON_AddStringToObject(root, "busiTime", timestamp);
    
    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送HTTP POST请求
    int ret = http_send_post_request(request_url, request_body, response_body);
    if (ret != 0) {
        qrzl_log("HTTP发送POST请求失败, ret=%d", ret);
        free(json_str);
        return CURL_REQUEST_ERROR;
    }
    // 解析返回结果
    cJSON *resp_json = cJSON_Parse(response_body);
    if (resp_json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return CURL_REQUEST_ERROR;
    }
    if (!resp_json || resp_json->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    // 提取并检查 code
    cJSON *j_code = cJSON_GetObjectItem(resp_json, "code");
    if (!j_code || !cJSON_IsString(j_code)) {
        qrzl_log("返回缺少或非法的 code 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    // 提取并检查 result，电信和移动返回的result类型不一样，移动返回的是object，电信返回的是number，
    // cJSON *j_result = cJSON_GetObjectItem(resp_json, "result");
    // if (!j_result || !cJSON_IsObject(j_result)) {
    //     qrzl_log("返回缺少或非法的 result 字段");
    //     cJSON_Delete(resp_json);
    //     return CURL_REQUEST_ERROR;
    // }    
    int code = atoi(j_code->valuestring);
    // 打印日志
    if (code == 0) {
        if(push_type == 1)
            qrzl_log("设备上线上报成功");
        else
            qrzl_log("设备下线上报成功");
        return CURL_REQUEST_OK;
    } else {
        qrzl_log("设备上报失败!");
         // 提取并检查 message
        cJSON *j_message = cJSON_GetObjectItem(resp_json, "message");
        if (j_message && j_message->type == cJSON_String) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
    }

    return CURL_REQUEST_ERROR;
}

/**
 * 光合 发送短信
 * @param terminalMac 终端mac
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int ghyc_send_sms(char *terminalMac, char *phoneNum)
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 接口路径
    char *interface_str = "/api/sendSms";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);
    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};
    
    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), '-');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), '-');

     // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    cJSON_AddStringToObject(root, "phoneNum", phoneNum);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送HTTP POST请求
    int ret = http_send_post_request(request_url, request_body, response_body);
    if (ret != 0) {
        qrzl_log("HTTP发送POST请求失败, ret=%d", ret);
        free(json_str);
        return CURL_REQUEST_ERROR;
    }

    // 解析返回结果
    cJSON *resp_json = cJSON_Parse(response_body);
    if (resp_json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return CURL_REQUEST_ERROR;
    }
    if (!resp_json || resp_json->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    // 提取并检查 code
    cJSON *j_code = cJSON_GetObjectItem(resp_json, "code");
    if (!j_code || !cJSON_IsString(j_code)) {
        qrzl_log("返回缺少或非法的 code 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    // 提取并检查 result
    cJSON *j_result = cJSON_GetObjectItem(resp_json, "result");
    if (!j_result || !cJSON_IsObject(j_result)) {
        qrzl_log("返回缺少或非法的 result 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    int code = atoi(j_code->valuestring);
    // 打印日志
    if (code == 0) {
        qrzl_log("GHYC -> 发送短信成功");
        return CURL_REQUEST_OK;
    } else {
        qrzl_log("GHYC -> 发送短信失败");
         // 提取并检查 message
        cJSON *j_message = cJSON_GetObjectItem(resp_json, "message");
        if (j_message && j_message->type == cJSON_String) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
    }

    return CURL_REQUEST_ERROR;
}
/**
 * 光合 终端设备登录认证
 * @param terminalMac 终端mac
 * @param verifyCode 验证码
 * @param phoneNum 手机号码
 * @return -1 请求失败， 0 请求成功
 */
int ghyc_terminal_auth(char *terminalMac, char *verifyCode, char *phoneNum)
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 接口路径
    char *interface_str = "/api/terminalAuth";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);
    char iccid_str[22] = {0};
    char mac_str[33] = {0};
    char terminalMac_str[33] = {0};
    
    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), '-');
    convert_mac_format(terminalMac, terminalMac_str, sizeof(terminalMac_str), '-');

     // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);
    cJSON_AddStringToObject(root, "phoneNum", phoneNum);
    cJSON_AddStringToObject(root, "verifyCode", verifyCode);
    
    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发送HTTP POST请求
    int ret = http_send_post_request(request_url, request_body, response_body);
    if (ret != 0) {
        qrzl_log("HTTP发送POST请求失败, ret=%d", ret);
        free(json_str);
        return CURL_REQUEST_ERROR;
    }

    // 解析返回结果
    cJSON *resp_json = cJSON_Parse(response_body);
    if (resp_json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return CURL_REQUEST_ERROR;
    }
    if (!resp_json || resp_json->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    // 提取并检查 code
    cJSON *j_code = cJSON_GetObjectItem(resp_json, "code");
    if (!j_code || !cJSON_IsString(j_code)) {
        qrzl_log("返回缺少或非法的 code 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    // 提取并检查 result
    cJSON *j_result = cJSON_GetObjectItem(resp_json, "result");
    if (!j_result || !cJSON_IsObject(j_result)) {
        qrzl_log("返回缺少或非法的 result 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }
    cJSON *j_code_token = cJSON_GetObjectItem(j_result, "codeToken");
    if (!j_code_token || !cJSON_IsString(j_code_token)) {
        qrzl_log("返回缺少或非法的 codeToken 字段");
        cJSON_Delete(resp_json);
        return CURL_REQUEST_ERROR;
    }

    int code = atoi(j_code->valuestring);
    // 打印日志
    if (code == 0) {
        qrzl_log("GHYC -> 终端设备认证成功");
        return CURL_REQUEST_OK;
    } else {
        qrzl_log("GHYC -> 终端设备认证失败");
         // 提取并检查 message
        cJSON *j_message = cJSON_GetObjectItem(resp_json, "message");
        if (j_message && j_message->type == cJSON_String) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
    }

    return CURL_REQUEST_ERROR;
}

/**
 * 光合 获取设备上网认证终端MAC
 */
char* ghyc_get_authed_list_str()
{
    init_customers_info();
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};

    // 接口路径
    char *interface_str = "/api/getTerminalList";
    snprintf(request_url, sizeof(request_url), "%s%s", one_link_customers_get_token_url, interface_str);

    char iccid_str[22] = {0};
    char mac_str[33] = {0};

    cfg_get_item("ziccid", iccid_str, sizeof(iccid_str));
    convert_mac_format(g_qrzl_device_static_data.mac, mac_str, sizeof(mac_str), '-');

    // 构建JSON请求体
    char request_body[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_str);
    cJSON_AddStringToObject(root, "iccid", iccid_str);

    // 手动请求的序列化字符串，由 malloc() 分配，需要手动释放。
    char *json_str = cJSON_PrintUnformatted(root);  // 转成紧凑字符串
    if (json_str) {
        strncpy(request_body, json_str, sizeof(request_body) - 1);
        request_body[sizeof(request_body) - 1] = '\0'; // 防止越界
        free(json_str); // 手动释放内部申请的内存
    }

    cJSON_Delete(root); // 释放 JSON 对象

    // 发起POST请求
    int request_res = http_send_post_request(request_url, request_body, response_body);

    if (request_res != 0) {
        return NULL;
    }

    // 解析 JSON 响应
    cJSON *json_response = cJSON_Parse(response_body);
    if (json_response == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        if (error_ptr != NULL) {
            qrzl_log("Error before: %s\n", error_ptr);
        }
        return NULL;
    }

    if (!json_response || json_response->type != cJSON_Object) {
        qrzl_log("响应格式错误或为空.");
        cJSON_Delete(json_response);
        return NULL;
    }

    cJSON *j_code = cJSON_GetObjectItem(json_response, "code");
    if (!j_code || j_code->type != cJSON_String) {
        qrzl_log("code is NULL or not a string.");
        cJSON_Delete(json_response);
        return NULL;
    }

    if (strcmp("0", j_code->valuestring) != 0) {
        qrzl_log("获取已认证终端设备MAC失败!");
        cJSON *j_message = cJSON_GetObjectItem(json_response, "message");
        if (j_message && j_message->type == cJSON_String) {
            qrzl_log("失败原因: %s", j_message->valuestring);
        }
        cJSON_Delete(json_response);
        return NULL;
    }

    // result 对象
    cJSON *j_result = cJSON_GetObjectItem(json_response, "result");
    if (!j_result || !cJSON_IsObject(j_result)) {
        qrzl_log("result is NULL or not a Object.");
        cJSON_Delete(json_response);
        return NULL;
    }

    // terminalList 数组
    cJSON *terminalList = cJSON_GetObjectItem(j_result, "terminalList");
    if (!terminalList || !cJSON_IsArray(terminalList)) {
        qrzl_log("terminalList is NULL or not an array.");
        cJSON_Delete(json_response);
        return NULL;
    }

    // 获取时间
    char datetime[20] = {0};
    get_local_datetime(datetime, sizeof(datetime)); 

    // 动态字符串构造
    size_t buffer_size = 5048;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(json_response);
        return NULL;
    }
    authed_mac_list[0] = '\0';
    int i;
    for (i = 0; i < cJSON_GetArraySize(terminalList); ++i) {
        cJSON *item = cJSON_GetArrayItem(terminalList, i);
        if (!item) continue;

        cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
        cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");
        if (!item_mac || !cJSON_IsString(item_mac) ||
            !expireTime || !cJSON_IsString(expireTime)) {
            continue;
        }

        // 时间比较
        if (strcmp(expireTime->valuestring, datetime) < 0) {
            continue;  // 已过期
        }

        // 转换 MAC 格式
        char formatted_mac[32] = {0};
        convert_mac_format(item_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

        // 拼接
        size_t used = strlen(authed_mac_list);
        size_t needed = strlen(formatted_mac) + 2;
        if (used + needed >= buffer_size) break;  // 超出，简单跳出

        strcat(authed_mac_list, formatted_mac);
        strcat(authed_mac_list, ";");
    }

    cJSON_Delete(json_response);
    return authed_mac_list;  // 需要调用者 free()
}