#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <pthread.h>
#include <string.h>

#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <time.h>
#include <signal.h>

#include "softap_api.h"
#include "qrzl_utils.h"
#include "qrzl_http_control_client.h"
#include "qrzl_mqtt_control_client.h"
#include "qrzl_device_control.h"
#include "cloud_control/xlx_control.h"
#include "cloud_control/hmm_control.h"
#include "cloud_control/qc_http_post_control.h"
#include "cloud_control/cshttp_control.h"
#include "cloud_control/one_link_http_control.h"
#include "cloud_control/cmp_auth_control.h"
#include "cloud_control/qc_tcp_control.h"
#include "cloud_control/jijia_http_control.h"
#ifdef QRZL_CLOUD_XUNYOU_MQTT
#include "cloud_control/xunyou_mqtt_control.h"
#endif
#include "curl/curl.h"	
#include "nv_api.h"

#ifdef JCV_FEATURE_CAPTIVE_PORTAL_SERVER
#include "qrzl_captive_portal_server.h"
#endif

#define LOCAL_AUTH_MAX_SIZE 5120  // 本地存储的字符串最长长度


/*create message queue*/
static int qrzl_create_msg_queue(int module_id)
{
    int msq_id = msgget(module_id, IPC_CREAT | 0600);
    if(msq_id == -1) {
        qrzl_err("failed to create msg queue module_id=%d, errno=%d\n", module_id, errno);
    }
    return msq_id;
}

/*wrapper of msgrcv*/
static ssize_t qrzl_msg_recv(int msqid, void* msgp, size_t msgsz,
                                long msgtype, int msgflag)
{
    return msgrcv(msqid, msgp, msgsz, msgtype, msgflag);
}

static void main_handle_set_band(MSG_BUF *msg)
{
    int band_num;
    memcpy(&band_num, msg->aucDataBuf, msg->usDataLen);
    set_lte_net_band(band_num);
}

static void main_handle_wifi_connected(MSG_BUF *msg)
{
    char mac_addr[32] = {0};

    if (msg->usDataLen > 0 && msg->usDataLen < sizeof(mac_addr)) {
        memcpy(mac_addr, msg->aucDataBuf, msg->usDataLen);
        mac_addr[msg->usDataLen] = '\0';  // 确保字符串结束
        qrzl_log("WiFi device connected, MAC: %s", mac_addr);

        // 这里可以添加具体的处理逻辑，比如：
        // 1. 记录连接的设备
        // 2. 调用运营商API进行认证
        // 3. 更新设备状态等
		int isp = 0;
		char imsi_str[30] = {0};
		char mac[33] = {0};
		cfg_get_item("sim_imsi", imsi_str, sizeof(imsi_str));
		isp = get_isp_by_imsi(imsi_str);
#if defined(QRZL_AUTH_ZHONGCHUANG_ONE_LINK) || defined(QRZL_AUTH_ZHONGCHUANG_CMP)
		char qrzl_cloud_authentic_switch[10]={0}; 
		cfg_get_item("qrzl_cloud_authentic_switch", qrzl_cloud_authentic_switch ,sizeof(qrzl_cloud_authentic_switch));
		if(strcmp(qrzl_cloud_authentic_switch,"1") == 0){
#endif
		// 1 china_mobile, 2 china_united, 3 china_telecom
		switch (isp)
		{
		case 1:
#ifdef QRZL_AUTH_ONE_LINK_HTTP
			one_link_push_device_info(1, mac_addr, "");
#endif
			break;
		case 2:
			/* code */
			break;
		case 3:
#ifdef QRZL_AUTH_CMP_HTTP
			cfg_get_item("wifi_mac", mac, sizeof(mac));
			device_line_type_push(1, mac, mac_addr, "");
#endif 
			break;

		default:
			break;
		}
        // 示例：可以在这里调用OneLink认证相关的处理
        // handle_onelink_authentication(mac_addr);
#if defined(QRZL_AUTH_ZHONGCHUANG_ONE_LINK) || defined(QRZL_AUTH_ZHONGCHUANG_CMP)
		}else{
			qrzl_log("Secondary authentication has not been enabled, so no report will be generated");
		}
#endif

    } else {
        qrzl_log("Invalid MAC address data received in WiFi connected message");
    }
}

static void main_handle_wifi_disconnect(MSG_BUF *msg)
{
    char mac_addr[32] = {0};

    if (msg->usDataLen > 0 && msg->usDataLen < sizeof(mac_addr)) {
        memcpy(mac_addr, msg->aucDataBuf, msg->usDataLen);
        mac_addr[msg->usDataLen] = '\0';  // 确保字符串结束
        qrzl_log("WiFi device disconnected, MAC: %s", mac_addr);

        // 这里可以添加具体的处理逻辑，比如：
        // 1. 清理连接记录
        // 2. 清理认证状态
        // 3. 更新设备状态等
		// 获取当前运营商
		int isp = 0;
		char imsi_str[30] = {0};
		char mac[33] = {0};
		cfg_get_item("sim_imsi", imsi_str, sizeof(imsi_str));
		isp = get_isp_by_imsi(imsi_str);
#if defined(QRZL_AUTH_ZHONGCHUANG_ONE_LINK) || defined(QRZL_AUTH_ZHONGCHUANG_CMP)
		char qrzl_cloud_authentic_switch[10]={0}; 
		cfg_get_item("qrzl_cloud_authentic_switch", qrzl_cloud_authentic_switch ,sizeof(qrzl_cloud_authentic_switch));
		if(strcmp(qrzl_cloud_authentic_switch,"1") == 0){
#endif
		// 1 china_mobile, 2 china_united, 3 china_telecom
		switch (isp)
		{
		case 1:
#ifdef QRZL_AUTH_ONE_LINK_HTTP
			one_link_push_device_info(0, mac_addr, "");
#endif
			break;
		case 2:
			/* code */
			break;
		case 3:
#ifdef QRZL_AUTH_CMP_HTTP
			cfg_get_item("wifi_mac", mac, sizeof(mac));
			device_line_type_push(0, mac, mac_addr, "");
#endif
			break;

		default:
			break;
		}
        // 示例：可以在这里清理OneLink认证相关的状态
        // cleanup_onelink_authentication(mac_addr);

#if defined(QRZL_AUTH_ZHONGCHUANG_ONE_LINK) || defined(QRZL_AUTH_ZHONGCHUANG_CMP)
		}else{
			qrzl_log("Secondary authentication has not been enabled, so no report will be generated");
		}
#endif

    } else {
        qrzl_log("Invalid MAC address data received in WiFi disconnect message");
    }
}

/* main thread, handle msg*/
static void main_handle_msg(MSG_BUF *msg)
{
    assert(msg);

    switch(msg->usMsgCmd) {
        /*set lte band*/
    case MSG_CMD_QRZL_APP_SET_BAND:
        main_handle_set_band(msg);
        break;

    case MSG_CMD_QRZL_APP_WIFI_CONNECTED:
        main_handle_wifi_connected(msg);
        break;

    case MSG_CMD_QRZL_APP_WIFI_DISCONNECT:
        main_handle_wifi_disconnect(msg);
        break;

    default:
        qrzl_log("Unhandled message command: %d\n",  msg->usMsgCmd);
    }
}

static short g_is_set_nv_sn = 0;

static void *init_nv_data_thread_handler()
{
	int ret = 0;

	char esim1_iccid[21] = {0};
	char esim1_imsi[16] = {0};

	char esim2_iccid[21] = {0};
	char esim2_imsi[16] = {0};
	
	char esim3_iccid[21] = {0};
	char esim3_imsi[16] = {0};

	nv_get_item(NV_RO, "esim1_iccid", esim1_iccid, sizeof(esim1_iccid));
	nv_get_item(NV_RO, "esim1_imsi", esim1_imsi, sizeof(esim1_imsi));
	nv_get_item(NV_RO, "esim2_iccid", esim2_iccid, sizeof(esim2_iccid));
	nv_get_item(NV_RO, "esim2_imsi", esim2_imsi, sizeof(esim2_imsi));
	nv_get_item(NV_RO, "esim3_iccid", esim3_iccid, sizeof(esim3_iccid));
	nv_get_item(NV_RO, "esim3_imsi", esim3_imsi, sizeof(esim3_imsi));


	cfg_set("esim1_iccid", esim1_iccid);
	cfg_set("esim1_imsi", esim1_imsi);
	cfg_set("esim2_iccid", esim2_iccid);
	cfg_set("esim2_imsi", esim2_imsi);
	cfg_set("esim3_iccid", esim3_iccid);
	cfg_set("esim3_imsi", esim3_imsi);


	// 根据imsi设置运营商
	nv_set_esim_mno(1, esim1_imsi);
	nv_set_esim_mno(2, esim2_imsi);
	nv_set_esim_mno(3, esim3_imsi);

	while (1)
	{
		if (g_is_set_nv_sn == 0)
		{	
			ret = nv_update_sn();
			g_is_set_nv_sn = 1;
		}
		else if (g_is_set_nv_sn == 0 && ret != 0)
		{
			/* sn号没有初始化，那么就等待一秒后重新开始循环 */
			sleep(1);
			continue;
		}
		nv_update_net_band();
		sleep(30);
	}
}

/**
 * 创建一个线程开始初始化nv流程
 */
static void start_init_nv_data()
{
	int err;
	pthread_t init_nv_data_tid;
	err = pthread_create(&init_nv_data_tid, NULL, init_nv_data_thread_handler, NULL);
	if (err != 0)
	{
		qrzl_err("创建初始化nv数据线程失败, error code: %d", err);
	}
}


/**
	开启云端控制客户端,使用一个线程来操作
 */
static void start_cloud_control_client()
{
	int cfg_ret;
	char cloud_protocol_type[20] = {0};
	int err;
	cfg_ret = cfg_get_item(NV_QRZL_CLOUD_PROTOCOL_TYPE, cloud_protocol_type, sizeof(cloud_protocol_type));
	if(cfg_ret != 0 || strlen(cloud_protocol_type) == 0 || strcmp(cloud_protocol_type, "NONE") == 0) {
		qrzl_log("qrzl_cloud_protocol_type 为空, 云接口被禁用");
		return;
	} 
	
	curl_global_init(CURL_GLOBAL_ALL);

#ifdef QRZL_DEVICE_CONTROL_ENABLE
	update_device_static_data();
	pthread_t qrzl_device_control_tid;
	err = pthread_create(&qrzl_device_control_tid, NULL, qrzl_device_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建qrzl_device_control_start线程失败, error code: %d", err);
	}

#endif

	/**
	 *  认证线程 - 使用编译时条件控制
	 */
#ifdef QRZL_AUTH_ONE_LINK_HTTP
	// {
	// 	pthread_t onelink_auth_tid;
	// 	int err_auth1 = pthread_create(&onelink_auth_tid, NULL, one_link_http_control_start, NULL);
	// 	qrzl_log("ONE_LINK_HTTP 设备上报线程启动....");
	// 	if (err_auth1 != 0) {
	// 		qrzl_err("创建one_link_http_control_start线程失败, error code: %d", err_auth1);
	// 	}
	// }
		one_link_http_control_init_config();
#endif

#ifdef QRZL_AUTH_CMP_HTTP
	// {
	// 	pthread_t cmp_auth_tid;
	// 	int err_auth2 = pthread_create(&cmp_auth_tid, NULL, cmp_http_control_start, NULL);
	// 	qrzl_log("CMP_HTTP 设备上报线程启动....");
	// 	if (err_auth2 != 0) {
	// 		qrzl_err("创建cmp_http_control_start线程失败, error code: %d", err_auth2);
	// 	}
	// }
		cmp_http_control_init_config();
#endif

	/**
	 * 云端协议客户端 - 使用编译时条件控制
	 */
	pthread_t tid;
	
#ifdef QRZL_CLOUD_HTTP
	err = pthread_create(&tid, NULL, start_http_control_client, NULL);
	if (err != 0) {
		qrzl_err("创建start_http_control_client 线程失败, error code: %d", err);
	}
	qrzl_log("启动HTTP云端控制客户端");
#elif defined(QRZL_CLOUD_MQTT)
	err = pthread_create(&tid, NULL, start_mqtt_control_client, NULL);
	if (err != 0) {
		qrzl_err("创建start_mqtt_control_client线程失败, error code: %d", err);
	}
	qrzl_log("启动MQTT云端控制客户端");
#elif defined(QRZL_CLOUD_XLX)
	err = pthread_create(&tid, NULL, xlx_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建xlx_control_start线程失败, error code: %d", err);
	}
	qrzl_log("启动XLX云端控制客户端");
#elif defined(QRZL_CLOUD_HMM_MQTT)
	err = pthread_create(&tid, NULL, hmm_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建hmm_control_start线程失败, error code: %d", err);
	}
	qrzl_log("启动HMM_MQTT云端控制客户端");
#elif defined(QRZL_CLOUD_QC_HTTP_POST)
	err = pthread_create(&tid, NULL, qc_http_post_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建qc_http_post_control_start线程失败, error code: %d", err);
	}
	qrzl_log("启动QC_HTTP_POST云端控制客户端");
#elif defined(QRZL_CLOUD_CS_HTTP)
	err = pthread_create(&tid, NULL, cshttp_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建cshttp_control_start线程失败, error code: %d", err);
	}
	qrzl_log("启动CS_HTTP云端控制客户端");
#elif defined(QRZL_CLOUD_QC_TCP)
	err = pthread_create(&tid, NULL, qc_tcp_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建qc_tcp_control_start线程失败, error code: %d", err);
	}
	qrzl_log("启动QC_TCP云端控制客户端");
#elif defined(QRZL_CLOUD_JIJIA_HTTP)
	err = pthread_create(&tid, NULL, jijia_http_control_start, NULL);
	if (err != 0) {
		qrzl_err("创建jijia_http_control_start线程失败, error code: %d", err);
	}
	qrzl_log("启动JIJIA_HTTP云端控制客户端");
#elif defined(QRZL_CLOUD_XUNYOU_MQTT)
	err = pthread_create(&tid, NULL, start_xunyou_mqtt_control_client, NULL);
	if (err != 0) {
		qrzl_err("创建xunyou_mqtt_control线程失败, error code: %d", err);
	}
	qrzl_log("启动XUNYOU_MQTT云端控制客户端");
#else
	qrzl_log("未选择任何云端协议类型");
#endif
}

/**
 * lte band 优先级设置 初始化
 */
static void lte_band_priority_init()
{
	int ret;
	char qrzl_lte_band_auto_switch_value[2] = {0};
	ret = cfg_get_item("qrzl_lte_band_auto_switch", qrzl_lte_band_auto_switch_value, 2);
	if (strcmp(qrzl_lte_band_auto_switch_value, "1") != 0)
	{
		qrzl_log("lte_band_priority_init not set");
		return;
	}
	
	// uint8_t b1_8, b9_16, b17_24, b25_32, b33_40, b41_48, b49_56, b57_64, b_not = 0;
	// char *p[] = {&b1_8, &b9_16, &b17_24, &b25_32, &b33_40, &b41_48, &b49_56, &b57_64, &b_not};
	// ret = get_modem_info2("AT+ZLTEAMTBAND?\r", "%hhu,%hhu,%hhu,%hhu,%hhu,%hhu,%hhu,%hhu,%hhu", (void**)p,0,10);
	int b1_8, b9_16, b17_24, b25_32, b33_40, b41_48, b49_56, b57_64, b_not = 0;
	char *p[] = {&b1_8, &b9_16, &b17_24, &b25_32, &b33_40, &b41_48, &b49_56, &b57_64, &b_not};
	char AT_ZLTEBANDPRI_str[256] = {0};
	char ZLTEBANDPRI_set_num[128] = {0};

	ret = get_modem_info2("AT+ZLTEAMTBAND?\r", "%d,%d,%d,%d,%d,%d,%d,%d,%d", (void**)p,0,10);
	if (ret == 0) {
		
		// snprintf(AT_ZLTEBANDPRI_str, sizeof(AT_ZLTEBANDPRI_str), "%s", "AT+ZLTEBANDPRI=");
		if (b41_48 & (1 << 0)) { // 41
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "41");
		}
		if (b33_40 & (1 << 5)) { // 38
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "38");
		}
		if (b33_40 & (1 << 7)) { // 40
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "40");
		}
		if (b1_8 & (1 << 0)) { // 1
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "1");
		}
		if (b1_8 & (1 << 2)) { // 3
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "3");
		}
		if (b33_40 & (1 << 6)) { // 39
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "39");
		}
		if (b1_8 & (1 << 7)) { // 8
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "8");
		}
		if (b1_8 & (1 << 4)) { // 5
			snprintf(ZLTEBANDPRI_set_num, sizeof(ZLTEBANDPRI_set_num), "%s%s,", ZLTEBANDPRI_set_num, "5");
		}
		if (strlen(ZLTEBANDPRI_set_num) > 0) {
			ZLTEBANDPRI_set_num[strlen(ZLTEBANDPRI_set_num)-1] = '\0';
		}
		qrzl_log("ZLTEBANDPRI_set_num: %s", ZLTEBANDPRI_set_num);
	}
	char ZLTEBANDPRI_resp[128] = {0};
    void *ZLTEBANDPRI_resp_p[] = {ZLTEBANDPRI_resp};
    ret = get_modem_info2("AT+ZLTEBANDPRI?\r", "%s",  (void**)&ZLTEBANDPRI_resp_p,0,10);
	qrzl_log("ZLTEBANDPRI_resp: %s", ZLTEBANDPRI_resp);
	if (strncmp(ZLTEBANDPRI_set_num, ZLTEBANDPRI_resp, sizeof(ZLTEBANDPRI_set_num)) != 0) {
		snprintf(AT_ZLTEBANDPRI_str, sizeof(AT_ZLTEBANDPRI_str), "AT+ZLTEBANDPRI=%s\r", ZLTEBANDPRI_set_num);
		qrzl_log("AT_ZLTEBANDPRI_str: %s", AT_ZLTEBANDPRI_str);
		get_modem_info2("AT+CFUN=0\r", NULL, NULL, 0, 10);
		get_modem_info2(AT_ZLTEBANDPRI_str, NULL, NULL, 0, 10);
		get_modem_info2("AT+CFUN=1\r", NULL, NULL, 0, 10);
	}
}

static void *let_net_auto_switch_band_thread()
{
	int ret;
	char qrzl_lte_band_auto_switch_value[2] = {0};
	ret = cfg_get_item("qrzl_lte_band_auto_switch", qrzl_lte_band_auto_switch_value, 2);
	if (strcmp(qrzl_lte_band_auto_switch_value, "1") != 0)
	{
		qrzl_log("let_net_auto_switch_band_thread not set");
		return NULL;
	}

	uint64_t up_speed_bps = 0;
	uint64_t down_speed_bps = 0;
	uint64_t speed_bps = 0;

	char realtime_tx_thrpt[21] = {0};
	char realtime_rx_thrpt[21] = {0};
    
	while (1)
	{
		
		ret = cfg_get_item("realtime_rx_thrpt", realtime_rx_thrpt, 21);
		if (ret == 0)
		{
			uint64_t rx_bytes = 0L;
			rx_bytes = atoll(realtime_rx_thrpt);
			down_speed_bps = (rx_bytes * 8);
		}
		ret = cfg_get_item("realtime_tx_thrpt", realtime_tx_thrpt, 21);
		if (ret == 0)
		{
			uint64_t tx_bytes = 0L;
			tx_bytes = atoll(realtime_tx_thrpt);
			up_speed_bps = (tx_bytes * 8);
		}

		// 如果 当前网速大于 13 Mbps 并且小于 20 Mbps就执行自动切换band
		speed_bps = up_speed_bps + down_speed_bps;
		qrzl_log("up_speed_bps: %lld, down_speed_bps: %lld, speed_bps: %lld", up_speed_bps, down_speed_bps, speed_bps);
		if (speed_bps > 4000000 && speed_bps < 13000000)
		{
			qrzl_log("start send AT+ZTRAFICMNT");
			get_modem_info2("AT+ZTRAFICMNT=1,60,5,15\r", NULL, NULL, 0, 10);
			get_modem_info2("AT+ZSET=\"RRC_CFG\",6\r", NULL, NULL, 0, 10);
			sleep(90);
		} else {
			sleep(5);
		}
	}
}

#ifdef QRZL_AUTH_MY
// MY 大流量用户优化方案，大流量跑时关闭WIFIdog，否则开启wifidog
static void *my_big_traffic_off_wifidog_thread()
{
	int ret;

	uint64_t up_speed_bps = 0;
	uint64_t down_speed_bps = 0;
	uint64_t speed_bps = 0;

	char realtime_tx_thrpt[21] = {0};
	char realtime_rx_thrpt[21] = {0};

	int wifi_dog_status = 1; // 0 表示未启动，1表示已启动
    
	while (1)
	{
		
		ret = cfg_get_item("realtime_rx_thrpt", realtime_rx_thrpt, 21);
		if (ret == 0)
		{
			uint64_t rx_bytes = 0L;
			rx_bytes = atoll(realtime_rx_thrpt);
			down_speed_bps = (rx_bytes * 8);
		}
		ret = cfg_get_item("realtime_tx_thrpt", realtime_tx_thrpt, 21);
		if (ret == 0)
		{
			uint64_t tx_bytes = 0L;
			tx_bytes = atoll(realtime_tx_thrpt);
			up_speed_bps = (tx_bytes * 8);
		}

		// 如果 当前网速大于 20 Mbps 就关闭wifidog
		// 如果 当前网速小于 10 Mbps 就开启wifidog
		speed_bps = up_speed_bps + down_speed_bps;
		qrzl_log("up_speed_bps: %lld, down_speed_bps: %lld, speed_bps: %lld", up_speed_bps, down_speed_bps, speed_bps);
		if (speed_bps > 20000000)
		{
			qrzl_log("big traffic! stoping wifidog!\n");

			if (wifi_dog_status == 0)
			{
				qrzl_log("WIFI Dog not running!");
				sleep(5);
				continue;
			}
			wifi_dog_status = 0;
			system("wdctl stop");
		} else if (speed_bps < 10000000) {
			
			if (wifi_dog_status == 1)
			{
				qrzl_log("WIFI Dog is running...");
				sleep(5);
				continue;
			}

			wifi_dog_status = 1;
			// 开启WIFIdog
			// 检查 /bin/wifidog 文件是否存在
			if (access("/bin/wifidog", F_OK) == 0) {
				qrzl_log("Starting wifidog...!!!\n");
			} else {
				qrzl_log("Starting wifidog fail!!!  /bin/wifidog is not exist !\n");
				return;
			}

			// 检查 /etc_rw/wifidog_onelink.conf 文件是否存在且不为空
			if (access("/etc_rw/wifidog_onelink.conf", F_OK) == 0) {
				// 注意：-s 选项检查文件是否为空。在 C 语言中，需要获取文件大小来判断。
				// 简单的 access() 无法判断大小，但通常在实际应用中会通过 stat() 或 fstat()
				// 获取文件信息。这里为了简化，我们只检查是否存在。
				
				// 执行命令 wifidog -c /etc_rw/wifidog_onelink.conf
				system("wifidog -c /etc_rw/wifidog_onelink.conf");
			} else {
				// 执行命令 wifidog -c /etc_ro/wifidog_onelink.conf
				system("wifidog -c /etc_ro/wifidog_onelink.conf");
			}

		}
		sleep(5);
	}
}

static void my_big_traffic_off_wifidog_init()
{
	int err;
	pthread_t my_big_traffic_off_wifidog_thread_tid;
	err = pthread_create(&my_big_traffic_off_wifidog_thread_tid, NULL, my_big_traffic_off_wifidog_thread, NULL);
	if (err != 0)
	{
		qrzl_err("crete my_big_traffic_off_wifidog_thread error code: %d", err);
	}
}

#endif

/**
 * 根据网速自动切换band
 */
static void lte_net_auto_switch_band_init()
{
	int err;
	pthread_t let_net_auto_switch_band_thread_tid;
	err = pthread_create(&let_net_auto_switch_band_thread_tid, NULL, let_net_auto_switch_band_thread, NULL);
	if (err != 0)
	{
		qrzl_err("crete let_net_auto_switch_band_thread error code: %d", err);
	}
}

/**
 * 初始化nv的 net_band_list 保存已校准的band
 */
static void init_nv_net_band_list()
{
	int ret;
	int b1_8, b9_16, b17_24, b25_32, b33_40, b41_48, b49_56, b57_64, b_not = 0;
	char *p[] = {&b1_8, &b9_16, &b17_24, &b25_32, &b33_40, &b41_48, &b49_56, &b57_64, &b_not};
	char net_band_list_str[128] = {0};

	ret = get_modem_info2("AT+ZLTEAMTBAND?\r", "%d,%d,%d,%d,%d,%d,%d,%d,%d", (void**)p,0,10);
	if (ret == 0) {		
		if (b1_8 & (1 << 0)) { // 1
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "1");
		}
		if (b1_8 & (1 << 2)) { // 3
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "3");
		}
		if (b1_8 & (1 << 4)) { // 5
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "5");
		}
		if (b1_8 & (1 << 7)) { // 8
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "8");
		}
		if (b33_40 & (1 << 5)) { // 38
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "38");
		}
		if (b33_40 & (1 << 6)) { // 39
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "39");
		}
		if (b33_40 & (1 << 7)) { // 40
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "40");
		}
		if (b41_48 & (1 << 0)) { // 41
			snprintf(net_band_list_str, sizeof(net_band_list_str), "%s%s,", net_band_list_str, "41");
		}
		
		if (strlen(net_band_list_str) > 0) {
			net_band_list_str[strlen(net_band_list_str)-1] = '\0';
		}
		cfg_set("net_band_list", net_band_list_str);
	}
}


void qrzl_app_init()
{
	// 首先设置band优先级，根据已校准的band来设置band优先级
	// lte_band_priority_init(); 不管用，先关了
	// 设置网速自动优先级
	lte_net_auto_switch_band_init();
	init_nv_net_band_list();
}

int main(int argc,char *argv[])
{
	int qrzl_msq = -1;
	int ret;
	MSG_BUF msg;

	qrzl_app_init();

	qrzl_utils_init();

	customer_customization_requirements_init();
	
	qrzl_set_process_name("qrzl_app");

	qrzl_msq = qrzl_create_msg_queue(MODULE_ID_QRZL_APP);
    if(qrzl_msq < 0) {
        qrzl_err("failed to create msq\n");
    }

	start_init_nv_data();

	start_cloud_control_client();
	
#ifdef JCV_FEATURE_CAPTIVE_PORTAL_SERVER
    start_captive_portal_server();
#endif

#if defined(JCV_FEATURE_CAPTIVE_PORTAL_SERVER) || defined(ZXIC_ONELINK_TEST)
	resolution_authentication_dns();
#endif

#ifdef QRZL_AUTH_MY
	my_big_traffic_off_wifidog_init();
#endif

	while (1)
	{
		memset(&msg, 0x00, sizeof(MSG_BUF));
        ret = qrzl_msg_recv(qrzl_msq, &msg, (sizeof(MSG_BUF) - sizeof(long)), 0, 0);
        if(ret <= 0)
            continue;
        qrzl_log("recv msg: %d\n",  msg.usMsgCmd);
        main_handle_msg(&msg);
	}

	curl_global_cleanup();

	return 0;
}
