#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>

#include "qc_http_post_control.h" 
#include "../qrzl_utils.h"
#include "../common_utils/cjson.h"
#include "curl/curl.h"
#include "softap_api.h"

/* 请求间隔时间 */
static int request_interval_time = 300;
/* http请求的路径 */
static char http_request_path[256] = {0};

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;
#define QRZL_HTTP_RESPONSE_MAX 5120

/**
 * 转换时间戳为字符串yyyymmddhhmmss
 * @param timestamp 时间戳
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 */
static void timestamp_to_string(time_t timestamp, char *buffer, size_t buffer_size)
{
    struct tm *time_info = localtime(&timestamp);
    strftime(buffer, buffer_size, "%Y%m%d%H%M%S", time_info);
}

 /**
 * HTTP响应回调函数
 * @param contents 接收到的数据指针
 * @param size 数据块大小
 * @param nmemb 数据块数量
 * @param userp 用户自定义指针(我们用来存储响应数据)
 * @return 处理的数据总大小
 */
// static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
//     size_t totalSize = size * nmemb;
//     if (totalSize >= QRZL_HTTP_RESPONSE_MAX) {
//         qrzl_err("http返回值大小: %d,大于已定义的最大长度", totalSize);
//         return totalSize;
//     }
//     strncat((char *)userp, (char *)contents, totalSize);
//     return totalSize;
// }
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *buffer = (char *)userp;
    size_t current_len = strlen(buffer);
    
    if ((current_len + totalSize) >= QRZL_HTTP_RESPONSE_MAX) {
        qrzl_err("http返回值大小超过最大长度限制，已截断");
        return 0;  // 返回0会中止传输
    }
    
    memcpy(buffer + current_len, contents, totalSize);
    buffer[current_len + totalSize] = '\0';
    return totalSize;
}

/**
 * 发送HTTP POST请求
 * @param url 请求URL
 * @param body 请求体(JSON格式)
 * @param response 响应缓冲区
 * @return 0成功, -1失败
 */
static int http_send_post_request(const char *url, const char *body, char *response)
{
    CURL *curl;
    CURLcode res;
    
    qrzl_log("HTTP POST请求 URL: %s", url);
    qrzl_log("HTTP POST请求体: %s", body);

    curl = curl_easy_init();
    if (curl) {
        // 设置请求URL
        curl_easy_setopt(curl, CURLOPT_URL, url);
        
        // 设置为POST请求
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        
        // 设置POST请求体
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
        
        // 设置HTTP头(Content-Type为application/json)
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        
        // 设置超时时间
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        
        // 设置响应回调函数
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        
        // 执行请求
        res = curl_easy_perform(curl);
        
        if (res != CURLE_OK) {
            qrzl_err("HTTP请求失败: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("HTTP响应: %s", response);
        }
        
        // 清理资源
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        
        return (res == CURLE_OK) ? 0 : -1;
    }
    return -1;
}

/**
 * 构建POST请求的JSON格式请求体
 * @param json_buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 */
static void qicheng_build_request_body(char *json_buffer, size_t buffer_size)
{   
    snprintf(json_buffer, buffer_size, "{");
    snprintf(json_buffer, buffer_size, "%s\"imei\":\"%s\"",json_buffer,g_qrzl_device_static_data.imei);
    snprintf(json_buffer, buffer_size, "%s,\"iccid\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.iccid);
    snprintf(json_buffer, buffer_size, "%s,\"mac\":\"%s\"", json_buffer, g_qrzl_device_static_data.mac);
    //KB单位
    snprintf(json_buffer, buffer_size, "%s,\"amount_end\":\"%llu\"", json_buffer, g_qrzl_device_dynamic_data.flux_month_total_bytes * 8 / 1024);
    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    time_t current_time = time(NULL);
    timestamp_to_string(current_time, time_string, sizeof(time_string));
    snprintf(json_buffer, buffer_size, "%s,\"current_time\":\"%s\"", json_buffer, time_string);
    snprintf(json_buffer, buffer_size, "%s,\"ssid\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(json_buffer, buffer_size, "%s,\"key\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.wifi_key_base64);
    snprintf(json_buffer, buffer_size, "%s,\"remainPwr\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.remain_power);
    snprintf(json_buffer, buffer_size, "%s,\"conn_cnt\":\"%d\"", json_buffer, g_qrzl_device_dynamic_data.conn_num);
    snprintf(json_buffer, buffer_size, "%s,\"mcc\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.mcc);
    snprintf(json_buffer, buffer_size, "%s,\"mnc\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.mnc);
    snprintf(json_buffer, buffer_size, "%s,\"lac\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.tac);
    snprintf(json_buffer, buffer_size, "%s,\"cid\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.cid);
    snprintf(json_buffer, buffer_size, "%s,\"soft_version\":\"%s\"", json_buffer, g_qrzl_device_static_data.soft_version);
    int wifistatus = 1;
    if (g_qrzl_device_dynamic_data.wifi_enable == 0) {
        wifistatus = 0;
    } else if (g_qrzl_device_dynamic_data.wifi_enable == 1 && g_qrzl_device_dynamic_data.wifi_hide == 1)
    {
        wifistatus = 2;
    }
    snprintf(json_buffer, buffer_size, "%s,\"wifistatus\":\"%d\"", json_buffer, wifistatus);
    snprintf(json_buffer, buffer_size, "%s,\"sn\":\"%s\"", json_buffer, g_qrzl_device_static_data.sn);
    snprintf(json_buffer, buffer_size, "%s,\"upspeed\":\"%llu\"", json_buffer, get_up_limit_net_speed() * 1000);
    snprintf(json_buffer, buffer_size, "%s,\"downspeed\":\"%llu\"", json_buffer, get_down_limit_net_speed() * 1000);
    snprintf(json_buffer, buffer_size, "%s,\"RSSI\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.rssi);
    snprintf(json_buffer, buffer_size, "%s,\"webPassword\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.web_password);
    snprintf(json_buffer, buffer_size, "%s,\"SINR\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.sinr);
    snprintf(json_buffer, buffer_size, "%s,\"PCI\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.pci);
    snprintf(json_buffer, buffer_size, "%s,\"IMSI\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.imsi);
    snprintf(json_buffer, buffer_size, "%s,\"Devicetype\":\"%s\"", json_buffer, g_qrzl_device_static_data.device_type);
    char apn_config_name[65] = {0};
    cfg_get_item("m_profile_name", apn_config_name, sizeof(apn_config_name));
    remove_spaces(apn_config_name);
    char apn_username[65] = {0};
    cfg_get_item("ppp_username", apn_username, sizeof(apn_username));
    char apn_password[65] = {0};
    cfg_get_item("ppp_passtmp", apn_password, sizeof(apn_password));
    char apn_login_number[32] = {0};
    cfg_get_item("wan_dial", apn_login_number, sizeof(apn_login_number));
    char apn_apn[32] = {0};
    cfg_get_item("wan_apn", apn_apn, sizeof(apn_apn));
    char apn_pdp_type[32] = {0};
    cfg_get_item("pdp_type", apn_pdp_type, sizeof(apn_pdp_type));
    char apn_auth_typee[32] = {0};
    cfg_get_item("ppp_auth_mode", apn_auth_typee, sizeof(apn_auth_typee));
    char apn[512] = {0};
    snprintf(apn, sizeof(apn), 
    "MCCMNC:%s%s;ConfigFileName:%s;UserName:%sPassword:%s;LoginNumber:%s;APN:%s;PDPType:%s;AuthType:%s;", 
    g_qrzl_device_dynamic_data.mcc, g_qrzl_device_dynamic_data.mnc, apn_config_name, apn_username, apn_password, apn_login_number, apn_apn, apn_pdp_type, apn_auth_typee);
    snprintf(json_buffer, buffer_size, "%s,\"apn\":\"%s\"", json_buffer,apn);
    snprintf(json_buffer, buffer_size, "%s,\"Wifi_filter_type\":\"%d\"", json_buffer, g_qrzl_device_dynamic_data.wifi_filter_type);
    snprintf(json_buffer, buffer_size, "%s,\"Blacklist\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.mac_black_list);
    snprintf(json_buffer, buffer_size, "%s,\"Whitelist\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.mac_white_list);
    snprintf(json_buffer, buffer_size, "%s,\"CurrentIp\":\"%s\"", json_buffer, g_qrzl_device_dynamic_data.current_wan_ip);
    snprintf(json_buffer, buffer_size, "%s,\"DualSIM\":\"%s\"", json_buffer, g_qrzl_device_static_data.dual_sim);
    int main_sim = 0;
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 0;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 1;
    } else {
        main_sim = 2;
    }
    snprintf(json_buffer, buffer_size, "%s,\"mainSIM\":\"%d\"", json_buffer, main_sim);
    snprintf(json_buffer, buffer_size, "%s}",json_buffer);
}

 
/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    int cfg_ret;
    cfg_ret = cfg_get_item(NV_QRZL_CLOUD_HTTP_PATH, http_request_path, 256);
    if (cfg_ret != 0 || http_request_path == NULL || strcmp(http_request_path, "") == 0)
    {
        qrzl_log("http_request_path is NULL");
        return -1;
    }
    
    // strcpy(http_request_path, "*************:8080/api/devicesev/statusUpdate");
    char cloud_request_interval_time[10] = {0};
    cfg_get_item(NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME, cloud_request_interval_time, 10);
    request_interval_time = atoi(cloud_request_interval_time);
    if (request_interval_time == 0) {
        request_interval_time = 300;
    }
    return 0;
}
static void qicheng_resp_handler(cJSON *value)
{
    
    int update_wifi_flag = 0;
    int save_nv_flag = 0;
    struct wifi_config_t wifi_config = {};
    init_wifi_config_value(&wifi_config);

    struct mac_filter_config_t mac_filter_config = {};
    init_mac_filter_config_value(&mac_filter_config);

    int restart_flag = 0;
    int reset_flag = 0;
    int update_mac_filter_flag = 0;

    // 遍历对象的键值对
    int i;
    for (i = 0; i < cJSON_GetArraySize(value); i++)
    {
        qrzl_log("JSON数组获取开始\n");
        cJSON * item=cJSON_GetArrayItem(value, i);
        qrzl_log("JSON数组获取成功\n");
        const char *key = item->string;
        qrzl_log("%s:%s\n",key,item->valuestring);
        
        if (!cJSON_IsString(item)) 
        {
            // 文档说所有的value都是字符串类型，如果不是字符串类型，那说明有异常，不管这个
            continue;
        }

        /* 限制速度，0 表 示不限速，限制 4G，任意速度， 单位为 Kbps，整数。 例如：256 */
        if (strcmp(key, "limitSpeed") == 0)
        {
            int limit_speed;
            limit_speed = atoi(item->valuestring);
            if (limit_speed >= 0)
            {   
                save_nv_flag |= 1;
                limit_net_speed(limit_speed, limit_speed);
            }
        } 
        /* 设备下次上报时间间隔，单位 秒 */
        else if (strcmp(key, "nextRptTime") == 0)
        {
            int next_rpt_time;
            next_rpt_time = atoi(item->valuestring);
            if (next_rpt_time != request_interval_time)
            {
                request_interval_time = next_rpt_time;
            }
            
        }
        /* 设备清算时间，datetime 格式，返回时间小于该设置时间时，设备清理缓存数据 */
        else if (strcmp(key, "clrStaticsTime") == 0)
        {
            
        }
        /* 服务器当前时间，datetime格式，用于设备时间校正 */
        else if (strcmp(key, "srvCurrTime") == 0)
        {
            
        }
        /* 设备下次上报流量间隔，单位 kb，暂时这个参 数没有使用，但 是需要预留 */
        else if (strcmp(key, "trafficRptThreshold") == 0)
        {
            
        }
        /* wifi 名称 */
        else if (strcmp(key, "ssidName") == 0)
        {
            update_wifi_flag |= 1;
            snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", item->valuestring);
        }
        /* wifi 密码 */
        else if (strcmp(key, "ssidPass") == 0)
        {
            if (strlen(item->valuestring) >= 8 && strlen(item->valuestring) < sizeof(wifi_config.key) - 1)
            {
                update_wifi_flag |= 1;
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", item->valuestring);
            }
        }
        /* 网络类型 4G/3G/2G/AUTO */
        else if (strcmp(key, "wan_type") == 0)
        {
            
        }
        /* 强制重置设备 1：强制重置 0：不重置 */
        else if (strcmp(key, "force_reset") == 0)
        {
            if (strcmp(item->valuestring, "1") == 0)
            {
                reset_flag |= 1;
            }
        }
        /* WIFI 状态 0 表示关闭，1 表示 打开，2 表示隐藏  SSID */
        else if (strcmp(key, "wifistatus") == 0)
        {
            if (strcmp(item->valuestring, "0") == 0)
            {
                wifi_config.enable = 0;
            }
            else if (strcmp(item->valuestring, "1") == 0)
            {
                wifi_config.enable = 1;
                wifi_config.hide = 0;
            }
            else if (strcmp(item->valuestring, "2") == 0)
            {
                wifi_config.enable = 1;
                wifi_config.hide = 1;
            }
            
            update_wifi_flag |= 1;
        }
        /* 强制重启设备 1 重启，0 不重启 */
        else if (strcmp(key, "Force_restart") == 0)
        {
            if (strcmp(item->valuestring, "1") == 0)
            {
                restart_flag |= 1;
            }
        }
        /* 设备检查新版本升级, 0 不检查，1 检查 */
        else if (strcmp(key, "DeviceUpgrade") == 0)
        {
            
        }
        /* 修改设备管理 端密码 */
        else if (strcmp(key, "webPassword") == 0)
        {
            save_nv_flag |= 1;
            update_web_password(item->valuestring);
        }
        /* 黑白名单模式 设置开启黑名单还 是白名单或都不开 启
0：正常模式
1： 白名单模式
2：黑名单模式
黑白名单模式与对  应列表需组合设置， 否则不生效。 */
        else if (strcmp(key, "Wifi_filter_type") == 0)
        {
            int wifi_filter_type;
            wifi_filter_type = atoi(item->valuestring);
            if (wifi_filter_type >= 0 && wifi_filter_type < 3)
            {   
                update_mac_filter_flag++;
                mac_filter_config.wifi_filter_type = wifi_filter_type;
            }
        }
        /* 设置黑名单 设置接入设备黑名 单
内容为 mac 地址列 表， 以分号相隔
22:06:B0:CA:33:CC ;22:06:B0:CA:33:CD */
        else if (strcmp(key, "Blacklist") == 0)
        {
            update_mac_filter_flag++;
            strlcpy(mac_filter_config.mac_black_list, item->valuestring, sizeof(mac_filter_config.mac_black_list));
        }
        /* 设置白名单
        设置接入设备白名 单
内容为 mac 地址列 表， 以分号相隔
22:06:B0:CA:33:CC ;22:06:B0:CA:33:C D
         */
        else if (strcmp(key, "Whitelist") == 0)
        {
            update_mac_filter_flag++;
            strlcpy(mac_filter_config.mac_white_list, item->valuestring, sizeof(mac_filter_config.mac_white_list));
        }
        /* 设置最大连接数, 最小1, 最大10 */
        else if (strcmp(key, "deviceCountSet") == 0)
        {
            update_wifi_flag |= 1;
            wifi_config.max_access_num = atoi(item->valuestring);
        }
        /* Wifi 加密方式 0：OPEN 1：WPA2(AES)-PSK  2：WPA-PSK/WPA2-PSK*/
        else if (strcmp(key, "apEncrypttype") == 0)
        {
            update_wifi_flag |= 1;
            if (strcmp(item->valuestring, "0") == 0)
            {
                strcpy(wifi_config.auth_mode, "OPEN");
            }
            else if (strcmp(item->valuestring, "1") == 0)
            {
                strcpy(wifi_config.auth_mode, "WPA2PSK");
            }
            else if (strcmp(item->valuestring, "2") == 0)
            {
                strcpy(wifi_config.auth_mode, "WPAPSKWPA2PSK");
            }
        }
        /* 切换卡槽 0 为卡槽 1,1 为卡槽 2（可用来区分实体 卡和贴片卡） */
        else if (strcmp(key, "switchSIM") == 0)
        {
            if (0 == strcmp(item->valuestring, "0"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(1);
            }
            else if (0 == strcmp(item->valuestring, "1"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(2);
            }
            else if (0 == strcmp(item->valuestring, "2"))
            {
                save_nv_flag |= 1;
                switch_sim_card_not_restart(0);
            }
            
        }
        /* 切换频段 例如：band8 则下发字符串 8；不支持组合频段，只能下发单个频段的值，设备未校准频段下发无效 */
        else if (strcmp(key, "band") == 0)
        {
            // 暂时只支持设置为自动
            if (strcmp(item->valuestring, "0"))
            {
                set_lte_net_band(0);
            }
        }

        // switch (cJSON_GetType(item)) {
        // case cJSON_String:
        //     qrzl_log("Value: %s (String)\n", item->valuestring);
        //     break;
        // case cJSON_Number:
        //     qrzl_log("Value: %d (Number)\n", item->valueint);
        //     break;
        // case cJSON_True:
        // case cJSON_False:
        //     qrzl_log("Value: %s (Boolean)\n", cJSON_IsTrue(item) ? "true" : "false");
        //     break;
        // default:
        //     qrzl_log("Value: (Unsupported Type)\n");
        // }
    }

    if (update_wifi_flag != 0)
    {
        save_nv_flag |= 1;
        update_wifi_by_config(&wifi_config);
    }
    if (update_mac_filter_flag > 0)
    {
        save_nv_flag |= 1;
        update_mac_filter_by_config(&mac_filter_config);
    }
    if (save_nv_flag != 0)
    {
        cfg_save();
    }
    if (restart_flag != 0)
    {
        restart_device();
    }
    if (reset_flag != 0)
    {
        reset_device();
    }
    
}
/**
    一次处理的主函数，发送请求，并根据请求做出相应的处理
 */
static void qicheng_start_process()
{
    int ret;
    char http_response[QRZL_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    char request_body[4096] = {0};

    update_device_dynamic_data();
   // 2. 构建JSON请求体
    qicheng_build_request_body(request_body, sizeof(request_body));
     // 3. 发送HTTP POST请求
    ret = http_send_post_request(http_request_path, request_body, http_response);
    if (ret != 0 || http_response[0] == '\0') {
        qrzl_err("HTTP请求失败或无响应");
        return;
    }
    
    cJSON *value = cJSON_Parse(http_response);
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (!cJSON_IsObject(value))
    {
        qrzl_err("JSON is not an object.\n");
        cJSON_Delete(value);
        return;
    }

    qicheng_resp_handler(value);

    // 释放 JSON 解析结果
    cJSON_Delete(value);
}

/**
 * 主要用来进入死循环，每隔一段时间开始发送一次http请求，并根据返回值处理
 */
void* qc_http_post_control_start()
{   qrzl_log("qc_http_post_start");
    // 1. 初始化配置
    if (init_config_data() != 0) {
        qrzl_err("初始化配置失败");
        return;
    }
    //这里的更新静态数据里的设备类型默认为MIFI
    update_device_static_data();
    //更改成UFI
    strcpy(g_qrzl_device_static_data.device_type, "UFI");
    while (1) 
    {
        qicheng_start_process();
        sleep(request_interval_time);
    }
    
}


