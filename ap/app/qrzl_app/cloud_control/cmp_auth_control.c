#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <wolfssl/wolfcrypt/sha.h>
#include <ctype.h>
#include <sys/time.h>

#include "../common_utils/cjson.h"
#include "cmp_auth_control.h"
#ifdef QRZL_AUTH_WUXING
#include "../auth_control/wuxing_auth_control.h"
#endif
#ifdef QRZL_AUTH_CMP_ORIGINAL
#include "../auth_control/cmp_original_auth_control.h"
#endif
#ifdef QRZL_AUTH_XUNYOU
#include "../auth_control/xunyou_auth_control.h"
#endif
#ifdef QRZL_AUTH_MY
#include "../auth_control/my_auth_control.h"
#endif
#ifdef QRZL_AUTH_CHUANGSAN
#include "../auth_control/chuangsan_auth_control.h"
#endif
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define MIN_INTERVAL_MS 2000  // 最小调用间隔：2秒 ，防抖时间（认证线程会频繁探测网络导致一秒内调用多次请求）

#define LOCAL_AUTH_MAX_SIZE 5120  // 本地存储的字符串最长长度

static char sign[254] = {0};
static char appKey[64] = {0};
static char timestamp[64] = {0};

static char cmp_auth_http_url[256] = {0};
static char cmp_auth_http_port[10] = {0};
static char cmp_customers_get_token_url[256] = {0};
static char cmp_customer_info[50] = {0};

// 上一次的 MAC 列表（分号分隔）
static char last_station_mac[2048] = {0};
static pthread_mutex_t last_station_mac_mutex = PTHREAD_MUTEX_INITIALIZER;  

// 记录上次调用的时间
static unsigned long last_request_time_ms = 0;

void device_line_type_push(const int push_type, const char *mac, const char *terminal_mac, const char *client_ip);


#define KEY_CMP_GET_TOKEN_URL       "cmp_customers_get_token_url"
#define KEY_CMP_URL                 "cmp_auth_http_url"
#define KEY_CMP_PORT                "cmp_auth_http_port"
#define KEY_CMP_CUSTOMER_INFO       "cmp_customer_info"

#define ONE_LINK_LOCAL_AUTH_INFO    "one_link_authed_mac"
#define CMP_LOCAL_AUTH_INFO         "cmp_authed_mac"
#define UNINET_LOCAL_AUTH_INFO      "uninet_authed_mac"

/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    cfg_get_item(KEY_CMP_GET_TOKEN_URL, cmp_customers_get_token_url, sizeof(cmp_customers_get_token_url));
    if (strlen(cmp_customers_get_token_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_GET_TOKEN_URL);
    }

    cfg_get_item(KEY_CMP_URL, cmp_auth_http_url, sizeof(cmp_auth_http_url));
    if (strlen(cmp_auth_http_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_URL);
    }

    cfg_get_item(KEY_CMP_PORT, cmp_auth_http_port, sizeof(cmp_auth_http_port));
    if (strlen(cmp_auth_http_port) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_PORT);
    }

    cfg_get_item(KEY_CMP_CUSTOMER_INFO, cmp_customer_info, sizeof(cmp_customer_info));
    if (strlen(cmp_customer_info) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_CUSTOMER_INFO);
    }

    return 0;
}

#ifdef QRZL_AUTH_QC_CMP
// QC获取电信相关信息封装便于轮询调用
void qc_cmp_get_token()
{
    qc_get_sign_code();
}
#endif

// 获取当前本地时间字符串，格式："YYYY-MM-DD HH:MM:SS"
void get_local_datetime(char *buffer, size_t size) {
    if (!buffer || size < 20) return;  // 安全检查：最小需 20 字节

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response, int type) {
    CURL *curl;
    CURLcode res;
    qrzl_log("ONE LINK -> http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        // 设置HTTP头
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
#ifdef QRZL_AUTH_ZHONGCHUANG_CMP
        char X_Device_Imei[128]={0};
        snprintf(X_Device_Imei,sizeof(X_Device_Imei),"X-Device-Imei:%s",g_qrzl_device_static_data.imei);
        headers = curl_slist_append(headers, X_Device_Imei);
#endif
        if (type == 1)
        {
            char h_sign[254] = {0};
            char h_appKey[64] = {0};
            char h_timestamp[254] = {0};
            snprintf(h_sign, sizeof(h_sign), "Sign: %s", sign);
            snprintf(h_appKey, sizeof(h_appKey), "AppKey: %s", appKey);
            snprintf(h_timestamp, sizeof(h_timestamp), "Timestamp: %s", timestamp);
            headers = curl_slist_append(headers, h_sign);
            headers = curl_slist_append(headers, h_appKey);
            headers = curl_slist_append(headers, h_timestamp);
        }
        // 设置公共参数
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_log("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (headers) {
            curl_slist_free_all(headers);
        }
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 通用的POST请求函数, type=1表示 将电信签名加入到请求头中
static int https_send_post_request_common(const char *url, const char* body, char *response, int type)
{
    CURL *curl;
    CURLcode res;

    // 初始化curl
    curl = curl_easy_init();

    if(curl == NULL) {
        qrzl_log("init CURL failed!!");
        return -1;
    }

    qrzl_log("request url: %s", url);
    qrzl_log("request body: %s", body);

    // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
    // 设置SSL
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    // 设置请求地址
    curl_easy_setopt(curl, CURLOPT_URL, url);
    // 使用post方式请求
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    // 设置响应超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    // 设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    // 回调函数
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    // 设置POST请求的内容
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
#ifdef QRZL_AUTH_ZHONGCHUANG_CMP
    char X_Device_Imei[128]={0};
    snprintf(X_Device_Imei,sizeof(X_Device_Imei),"X-Device-Imei:%s",g_qrzl_device_static_data.imei);
    headers = curl_slist_append(headers, X_Device_Imei);
#endif
    if (type == 1)
    {
        char h_sign[254] = {0};
        char h_appKey[64] = {0};
        char h_timestamp[254] = {0};
        snprintf(h_sign, sizeof(h_sign), "Sign: %s", sign);
        snprintf(h_appKey, sizeof(h_appKey), "AppKey: %s", appKey);
        snprintf(h_timestamp, sizeof(h_timestamp), "Timestamp: %s", timestamp);
        headers = curl_slist_append(headers, h_sign);
        headers = curl_slist_append(headers, h_appKey);
        headers = curl_slist_append(headers, h_timestamp);
    }
    // 设置公共参数
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    // 设置 接收请求响应的内容
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    // 正式发起请求
    res = curl_easy_perform(curl);

    // 处理返回值
    if(res != CURLE_OK) {
        qrzl_log("curl_easy_perform request failed!");
    }

    if (headers) {
        curl_slist_free_all(headers);
    }
    // 清理内存
    curl_easy_cleanup(curl);

    if (res != CURLE_OK)
    {
        return -1;
    }

    qrzl_log("request response: %s\n", response);

    return 0;
}

// 填充mac数组
int cmp_split_mac_list(const char *src, char macs[][32], int max_macs) {
    int count = 0;
    char buf[2048];
    // 将mac复制到缓冲区
    strncpy(buf, src, sizeof(buf));
    // 分割mac
    char *token = strtok(buf, ";");
    while (token && count < max_macs) {
        // 将mac填充到二维数组中
        strncpy(macs[count++], token, 31);
        token = strtok(NULL, ";");
    }
    return count;
}

// 检测设备离线
void cmp_check_device_offline(const char *prev_list, const char *current_list) {

    // 认证开关
    char auth_switch[2] = {0};
    get_authentic_switch(auth_switch, sizeof(auth_switch));
    if (strcmp(auth_switch, "0") == 0) {
        qrzl_log("认证开关未开启，停止检测上报设备下线.");
        return;
    }

    // 定义二维数组，数组个数最大200，因为设备最多只能连接200个
    char prev[200][32], curr[200][32];
    // 填充上次mac数组和当前mac数组
    int prev_cnt = cmp_split_mac_list(prev_list, prev, 200);
    int curr_cnt = cmp_split_mac_list(current_list, curr, 200);
    int i, j;
    // 循环上次的mac数组
    for (i = 0; i < prev_cnt; ++i) {
        int found = 0;
        // 内循环当前的mac数组，用于每次比较
        for (j = 0; j < curr_cnt; ++j) {
            // 如果上次的mac数组的元素，存在于当前数组，则表示设备在线，设置标识位并跳出内循环
            if (strcmp(prev[i], curr[j]) == 0) {
                found = 1;
                break;
            }
        }
        // 如果上次的mac数组的元素不在当前数组，说明设备下线了
        if (!found) {
            // 离线了
            qrzl_log("离线设备MAC：%s", prev[i]);
            // 地址格式转换 上报设备下线
            char mac_buff[40] = {0};
            char terminalMac_buff[40] = {0};
            char authentic_switch[10] = {0};
            cfg_get_item("qrzl_cloud_authentic_switch", authentic_switch, sizeof(authentic_switch));
            if(strcmp(authentic_switch,"1") == 0 || strlen(authentic_switch) == 0 ) {
                device_line_type_push(0, g_qrzl_device_static_data.mac, prev[i], "");
            }
        }
    }
}

/**
 * 判断 MAC 是否在 本地认证 列表中（分号分隔）
 * @param list 本地认证信息
 * @param mac 终端mac
 * @return 0 不存在， 1 存在
 */
static int mac_in_local_auth_list(const char *list, const char *mac) {
    if (!list || !mac) return 0;

    const char *p = list;
    size_t mac_len = strlen(mac);
    while (p && *p) {
        const char *sep = strchr(p, ';');
        size_t len = sep ? (size_t)(sep - p) : strlen(p);
        if (len == mac_len && strncasecmp(p, mac, mac_len) == 0) {
            return 1;
        }
        p = sep ? sep + 1 : NULL;
    }
    return 0;
}

/**
 * 通过接口返回已认证信息，更新到本地
 * @return 0 成功， 1 失败
 */
int update_local_auth_info_for_cmp_api()
{   
    int isp = get_current_isp();

    if (isp != 3) {
        return -1;
    }

    char *new_auth_str = NULL;

#ifdef QRZL_CMP_CUSTOMER_WUXING
    new_auth_str = wuxing_check_device_authed();
#elif defined(QRZL_AUTH_CMP_ORIGINAL)
#ifndef QRZL_AUTH_QC_CMP
    init_cmp_config_data();
    // 电信 原生认证检测
#else
    //齐成获取sign
    init_qc_cmp_config_data();
#endif
    new_auth_str = cmp_check_device_authed();
#elif defined(QRZL_AUTH_XUNYOU)
    // xunyou 认证检测
    new_auth_str = xunyou_check_device_authed();
#elif defined(QRZL_AUTH_CHUANGSAN)
    new_auth_str = cs_one_link_get_authed();
#elif defined(QRZL_AUTH_MY)
    new_auth_str = my_get_authed_list_str();
#elif defined(QRZL_AUTH_GUANGHEYUNCHUANG)
    new_auth_str = ghyc_get_authed_list_str();
#elif defined(QRZL_AUTH_KY)
    new_auth_str = ky_get_authed_list_str();
#else
    // Default to original cmp auth
#endif

    if (new_auth_str == NULL || !new_auth_str) {
        qrzl_log("接口获取认证信息失败，new_auth_str is NULL");
        free(new_auth_str);
        return -1;
    }

    if (strlen(new_auth_str) >= LOCAL_AUTH_MAX_SIZE) {
        qrzl_log("接口获取认证信息MAC 列表过长，无法保存\n");
        free(new_auth_str);
        return -1;
    }

    qrzl_log("yang - new_auth_str: %s, CMP_LOCAL_AUTH_INFO: %s", new_auth_str, CMP_LOCAL_AUTH_INFO);

    // 更新接口新认证信息到本地
    // cfg_set(CMP_LOCAL_AUTH_INFO, new_auth_str);
    update_authed_mac_list(CMP_LOCAL_AUTH_INFO, new_auth_str);
    
    free(new_auth_str);
    return 0;
}

/**
 * 获取本地认证信息
 */
char *get_loacl_auth_info()
{
    int isp = get_current_isp();

    char local_auth_info[LOCAL_AUTH_MAX_SIZE] = {0};
    
    if (isp == 1) {
        // 获取移动已认证的mac列表
        cfg_get_item(ONE_LINK_LOCAL_AUTH_INFO, local_auth_info, sizeof(local_auth_info));
    } else if (isp == 2) {
        // 获取联通已认证的mac列表
        cfg_get_item(UNINET_LOCAL_AUTH_INFO, local_auth_info, sizeof(local_auth_info));
    } else if (isp == 3) {
        // 获取电信已认证的mac列表
        cfg_get_item(CMP_LOCAL_AUTH_INFO, local_auth_info, sizeof(local_auth_info));
    } else {
        qrzl_log("未知运营商isp: %d ", isp);
        return "";
    }

    return local_auth_info;
}

/**
 * 判断终端设备是否已经认证
 * @param mac 设备MAC
 * @param terminal_mac 终端设备MAC
 * @return 0 未认证； 1 已认证
 */
int cmp_terminal_is_authed(const char* mac, const char* terminal_mac, const char* servAddr)
{   

    // if (strncmp(cmp_customer_info, "WUXING", sizeof(cmp_customer_info)) == 0) {
    //     // wuxing 认证检测
    //     return wuxing_is_mac_authed(terminal_mac);
    // } else if (strncmp(cmp_customer_info, "ORIGIN", sizeof(cmp_customer_info)) == 0) {
    //     init_cmp_config_data();
    //     // 电信 原生认证检测
    //     return cmp_is_mac_authed(terminal_mac);
    // } else if (strncmp(cmp_customer_info, "XUNYOU", sizeof(cmp_customer_info)) == 0) {
    //     // xunyou 认证检测
    //     return xunyou_is_mac_authed(terminal_mac);
    // } else {
    //     // 获取已认证的mac列表
    //     char cmp_authed_mac_list[1024] = {0};
    //     cfg_get_item("cmp_authed_mac", cmp_authed_mac_list, sizeof(cmp_authed_mac_list));
    //     // 本地存储认证检测
    //     return is_mac_authenticated_with_timeout(terminal_mac, cmp_authed_mac_list, 60*60*24*30);
    // }


    // * 读取本地存储的认证信息，避免接口频繁调用，或网络原因导致认证加载延迟
    int isp = get_current_isp();
    // 本地数据过期时间 单位 s
    int timeout_auth = 60*60*24*30;
    // 获取本地认证信息
    char *authed_mac_list = get_loacl_auth_info();

    char terminal_mac_format[33] = {0};
    // 将mac转成:分割格式
    convert_mac_format(terminal_mac, terminal_mac_format, sizeof(terminal_mac_format), ':');

    qrzl_log("terminal_mac: %s", terminal_mac_format);
    qrzl_log("authed_mac_list: %s", authed_mac_list);

    return mac_in_local_auth_list(authed_mac_list, terminal_mac_format);
    
}


/**
 * 设备上/下线上报
 * @param push_type 上报类型 1上线， 0下线
 * @param mac 设备MAC
 * @param terminal_mac 终端设备MAC
 */
void device_line_type_push(const int push_type, const char *mac, const char *terminal_mac, const char *terminalIp)
{   
    char qrzl_cloud_authentic_switch[10]={0}; 
	cfg_get_item("qrzl_cloud_authentic_switch", qrzl_cloud_authentic_switch ,sizeof(qrzl_cloud_authentic_switch));
	if(strcmp(qrzl_cloud_authentic_switch,"0") == 0){ 
		qrzl_log("Secondary authentication has not been enabled. No connect_status report will be made.");
		return;
	}
#ifdef QRZL_CMP_CUSTOMER_WUXING
    // wuxing 上报
    char *type = push_type == 1 ? "1" : "2";
    wuxing_device_line_type_push(type, mac, terminal_mac);
#elif defined(QRZL_AUTH_XUNYOU)
    // 迅优 原生上报
    xunyou_device_line_type_push(push_type, mac, terminal_mac, "");
#elif defined(QRZL_AUTH_CMP_ORIGINAL)
    // 电信 原生上报
    cmp_device_line_type_report(mac, terminal_mac, push_type);
#elif defined(QRZL_AUTH_CHUANGSAN)
    // chuangsan接口上报
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);
    cs_one_link_on_offline(push_type, g_qrzl_device_dynamic_data.iccid, g_qrzl_device_static_data.mac, terminal_mac, terminalIp);
#elif defined(QRZL_AUTH_MY)
    my_report_device_line_state(push_type, terminal_mac);
#elif defined(QRZL_AUTH_GUANGHEYUNCHUANG)
    ghyc_report_device_line_state(push_type, terminal_mac);
#elif defined(QRZL_AUTH_KY)
    ky_report_device_line_state(push_type, terminal_mac);
#else
    // Default to original cmp auth
#endif
}


int cmp_check_mac_in_last_station(char *station_mac) {
    pthread_mutex_lock(&last_station_mac_mutex);
    int ret = 0;
    if (strlen(last_station_mac) > 0) {
        if (strstr(last_station_mac, station_mac) != NULL) {
            // 如果上次记录的mac列表中已经存在当前mac，则返回1
            ret = 1;
        }
    }
    pthread_mutex_unlock(&last_station_mac_mutex);
    return ret;
}

void cmp_change_last_station_mac(char *station_mac) {
    update_device_dynamic_data();
    pthread_mutex_lock(&last_station_mac_mutex);
    //strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);
    if(strlen(last_station_mac)>0){
        if(strstr(last_station_mac, station_mac) != NULL){
            // 如果上次记录的mac列表中已经存在当前mac，则不进行更新
            pthread_mutex_unlock(&last_station_mac_mutex);
            return;
        }
        strcat(last_station_mac, ";");
        strcat(last_station_mac, station_mac);
    }
    else{
        strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);
    }
    pthread_mutex_unlock(&last_station_mac_mutex);
}

void* cmp_device_offline_thread()
{
    while (1)
    {
        int last_station_mac_len = 0;
        // 获取当前卡的IMSI
        char sim_imsi[32] = {0};
        cfg_get_item("sim_imsi", sim_imsi, 32);
        // 通过imsi查询运营商
        int isp = get_isp_by_imsi(sim_imsi);
        // 运营商不是电信则不进行下面的动作
        if (isp != 3) {
            continue;
        }

        pthread_mutex_lock(&last_station_mac_mutex);

        update_device_dynamic_data();
        char station_mac[2048] = {0};
        int ret = cfg_get_item("station_mac", station_mac, sizeof(station_mac));
        if (ret != 0) {
            qrzl_log("cmp_device_offline_thread -> station_mac 读取失败或未初始化!");
        }
        qrzl_log("cmp_device_offline_thread -> station_mac : %s ", station_mac);
        last_station_mac_len = strlen(last_station_mac);

        // 比较当前mac列表与上次记录mac列表
        if (last_station_mac_len > 0) {
            cmp_check_device_offline(last_station_mac, station_mac);
        }
        
        // 更新上次记录mac列表
        strncpy(last_station_mac, station_mac, sizeof(last_station_mac) - 1);

        pthread_mutex_unlock(&last_station_mac_mutex);
        // 每30秒检测一次设备下线
        sleep(30);
    }
    return NULL;
}

// 获取第三方认证页面地址
int cmp_get_customers_page_url(char *customers_page_url, int url_len, char *mac, char *terminalMac, char *client_ip, char *servAddr)
{   
    qrzl_log("开始构建认证页面");
    // 清理customers_page_url，避免残留数据
    if (customers_page_url && url_len > 0) {
        memset(customers_page_url, 0, url_len);
        qrzl_log("清理customers_page_url残留数据..");
    }

#if defined(QRZL_CMP_CUSTOMER_WUXING) || defined(QRZL_CMP_CUSTOMER_JIUYAO)
    // 尝试获取客户自定义的认证页面
    int try_count = 3;
    while (try_count > 0 && cfg_get_item("cmp_customers_auth_page_url", customers_page_url, url_len) != 0)
    {   
        try_count --;
        sleep(1);
    }
#elif defined(QRZL_CMP_CUSTOMER_MY)
    // MAC
    char convered_mac[33] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), '\0');
    // terminalMac
    char convered_terminalMac[33] = {0};
    convert_mac_format(terminalMac, convered_terminalMac, sizeof(convered_terminalMac), '\0');
    // iccid
    char current_iccid[22] = {0};
    cfg_get_item("ziccid", current_iccid, sizeof(current_iccid));
    // sn
    char sn[20] = {0};
    snprintf(sn, sizeof(sn), "%s", g_qrzl_device_static_data.sn);
    // sequence
    int sim_index = get_device_current_sim_index();
    char sequence[2] = {0};
    switch (sim_index)
    {
        case 0:
            snprintf(sequence, sizeof(sequence), "%s", "3");
            break;
        case 1:
            snprintf(sequence, sizeof(sequence), "%s", "1");
            break;
        case 2:
            snprintf(sequence, sizeof(sequence), "%s", "2");
            break;
    }

    char lan_ip[30] = {0};
    cfg_get_item("lan_ipaddr", lan_ip, sizeof(lan_ip));

    char device_ip_orign[2048] = {0};
    char device_ip_encoded[2048] = {0};
    snprintf(device_ip_orign, sizeof(device_ip_orign), "http://%s/Api/codeToken", lan_ip);
    url_encode(device_ip_orign, device_ip_encoded);

    snprintf(customers_page_url, url_len, "%s/boss-web/wx-login.html?mac=%s&terminalMac=%s&phoneNum=%s&iccid=%s&sn=%s&sequence=%s&deviceIp=%s", 
                cmp_customers_get_token_url, convered_mac, convered_terminalMac, "", current_iccid, sn, sequence, device_ip_encoded);

#elif defined(QRZL_CMP_CUSTOMER_BEIWEI)
    bw_build_customer_url(customers_page_url, url_len, terminalMac, 3);
#elif defined(QRZL_CMP_CUSTOMER_CHUANGSAN)
    cs_build_customer_url(customers_page_url, url_len, terminalMac);
#else
    // MAC
    char convered_mac[33] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), '\0');
    // terminalMac
    char convered_terminalMac[33] = {0};
    convert_mac_format(terminalMac, convered_terminalMac, sizeof(convered_terminalMac), '\0');
    snprintf(customers_page_url, url_len, "https://cmp-authportal.ctwing.cn:20153/#/?terminalMac=%s&mac=%s&servAddr=%s", convered_terminalMac, convered_mac, servAddr);
#endif

    if (strlen(customers_page_url) > 0) {
        qrzl_log("电信 跳转地址为：%s", customers_page_url);
    } else {
        qrzl_log("电信 第三方跳转地址为NULL，使用内置页面");
    }

    return 0;
}

void cmp_auth_start_process()
{
    int cmp_device_offline_thread_err;
    pthread_t cmp_device_offline_thread_tid;

    // 开启检测设备下线线程
    cmp_device_offline_thread_err = pthread_create(&cmp_device_offline_thread_tid, NULL, cmp_device_offline_thread, NULL);
    if (cmp_device_offline_thread_err != 0) {
        qrzl_err("创建cmp_device_offline_thread线程失败, error code: %d", cmp_device_offline_thread_err);
    }
}

void init_cmp_authed_info()
{   

#ifndef QRZL_AUTH_CMP_HTTP
    // 如果未定义 QRZL_AUTH_CMP_HTTP 宏则直接返回
    qrzl_log("未开启电信认证，不进行本地认证信息更新.");
    return;
#endif
    char qrzl_cloud_authentic_switch[10]={0}; 
	cfg_get_item("qrzl_cloud_authentic_switch", qrzl_cloud_authentic_switch ,sizeof(qrzl_cloud_authentic_switch));
	if(strcmp(qrzl_cloud_authentic_switch,"0") == 0){ 
		qrzl_log("Secondary authentication has not been enabled.The secondary authentication list has not been initialized.");
		return;
	}
#ifdef QRZL_AUTH_ZHONGCHUANG_CMP
	char qrzl_cloud_authentic_switch[10]={0}; 
	cfg_get_item("qrzl_cloud_authentic_switch", qrzl_cloud_authentic_switch ,sizeof(qrzl_cloud_authentic_switch));
	if(strcmp(qrzl_cloud_authentic_switch,"0") == 0){ 
        qrzl_log("Secondary authentication has not been enabled. The secondary authentication list has not been initialized.");
        return;
    }
#endif

    // 等待网络上网完成 
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }

    qrzl_log("开始初始化电信本地认证信息....");
    if (get_current_isp() != 3) {
        qrzl_log("非电信卡，结束重新获取");
        return;
    }
    // 初始化时更新一次本地认证信息
    int try_update_count = 1;
    int wait_time = 3;
    while (update_local_auth_info_for_cmp_api() != 0 && try_update_count <= 5) {
        qrzl_log("第 %d 次尝试更新本地认证信息失败，%d秒后尝试重新更新...", try_update_count, wait_time);
        try_update_count++;
        sleep(wait_time);
    }
}

void* cmp_http_control_init_config()
{
    // 初始化配置信息
    init_config_data();

    init_cmp_authed_info();

    // cmp_auth_start_process();
}
